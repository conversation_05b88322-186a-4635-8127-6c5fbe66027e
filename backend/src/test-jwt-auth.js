const axios = require('axios');

const API_URL = 'http://localhost:5000/api';

// Test email OTP flow
async function testEmailOTP() {
  try {
    // 1. Send OTP to email
    console.log('1. Sending OTP to email...');
    const sendOTPResponse = await axios.post(`${API_URL}/auth/send-otp`, {
      email: '<EMAIL>'
    });
    console.log('OTP sent successfully:', sendOTPResponse.data);
    
    // 2. In a real scenario, the user would check their email and input the OTP
    // For testing, we'll just ask for the OTP manually
    const otp = await promptForOTP();
    
    console.log('2. Verifying OTP...');
    const verifyOTPResponse = await axios.post(`${API_URL}/auth/verify-otp`, {
      email: '<EMAIL>',
      otp: otp
    });
    console.log('OTP verified successfully:', verifyOTPResponse.data);
    
    const token = verifyOTPResponse.data.data.token;
    
    // 3. Register the rider with JWT
    console.log('3. Registering rider with JWT...');
    const registerResponse = await axios.post(
      `${API_URL}/auth/register/jwt`,
      {
        gstNumber: '27AADCB2230M1Z6',
        companyName: 'Test Company',
        contactPerson: 'Test User',
        contactNumber: '9876543210',
        email: '<EMAIL>',
        paymentTerms: 'IMMEDIATE'
      },
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    console.log('Registration successful:', registerResponse.data);
    
    // 4. Get user profile with JWT
    console.log('4. Getting user profile with JWT...');
    const profileResponse = await axios.get(
      `${API_URL}/auth/profile`,
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    console.log('Profile retrieved successfully:', profileResponse.data);
    
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

// Simple function to prompt for OTP (in a real app, this would come from user input)
function promptForOTP() {
  return new Promise((resolve) => {
    // In a real scenario, you would get this from user input
    // For testing, you can manually check your email and input the OTP here
    console.log('Please check your email and enter the OTP you received:');
    process.stdin.once('data', (data) => {
      resolve(data.toString().trim());
    });
  });
}

// Run the test
testEmailOTP();
