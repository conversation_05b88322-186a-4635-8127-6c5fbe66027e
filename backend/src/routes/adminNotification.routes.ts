/**
 * Admin Notification Routes
 * Routes for admin notification management
 */

import { Router } from 'express';
import { authenticateAdmin } from '../middlewares/auth.middleware';
import {
  getAdminNotifications<PERSON><PERSON><PERSON>,
  getUnreadAdmin<PERSON>otification<PERSON>ou<PERSON><PERSON><PERSON><PERSON>,
  markAdminNotificationAs<PERSON><PERSON><PERSON>and<PERSON>,
  markAllAdminNotificationsAsReadHandler,
  deleteAdminNotificationHandler,
} from '../controllers/adminNotification.controller';

const router = Router();

/**
 * @swagger
 * /api/admin-notifications:
 *   get:
 *     summary: Get admin notifications with pagination and filters
 *     tags: [Admin Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: unreadOnly
 *         schema:
 *           type: boolean
 *         description: Filter to show only unread notifications
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [LOW, NORMAL, HIGH, URGENT]
 *         description: Filter by priority
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [SYSTEM, USER_ACTION, ORDER, PAYMENT, STOCK, ENQUIRY]
 *         description: Filter by category
 *     responses:
 *       200:
 *         description: List of admin notifications
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/', authenticateAdmin, getAdminNotificationsHandler);

/**
 * @swagger
 * /api/admin-notifications/unread-count:
 *   get:
 *     summary: Get unread admin notification count
 *     tags: [Admin Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Unread notification count
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/unread-count', authenticateAdmin, getUnreadAdminNotificationCountHandler);

/**
 * @swagger
 * /api/admin-notifications/{id}/read:
 *   patch:
 *     summary: Mark admin notification as read
 *     tags: [Admin Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Notification ID
 *     responses:
 *       200:
 *         description: Notification marked as read
 *       400:
 *         description: Invalid notification ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Notification not found
 */
router.patch('/:id/read', authenticateAdmin, markAdminNotificationAsReadHandler);

/**
 * @swagger
 * /api/admin-notifications/read-all:
 *   patch:
 *     summary: Mark all admin notifications as read
 *     tags: [Admin Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: All notifications marked as read
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.patch('/read-all', authenticateAdmin, markAllAdminNotificationsAsReadHandler);

/**
 * @swagger
 * /api/admin-notifications/{id}:
 *   delete:
 *     summary: Delete admin notification
 *     tags: [Admin Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Notification ID
 *     responses:
 *       200:
 *         description: Notification deleted successfully
 *       400:
 *         description: Invalid notification ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Notification not found
 */
router.delete('/:id', authenticateAdmin, deleteAdminNotificationHandler);

export default router;
