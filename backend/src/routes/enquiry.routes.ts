import { Router } from 'express';
import { authenticateRider } from '../middlewares/auth.middleware';
import validate from '../middlewares/validate.middleware';
import {
  createEnquirySchema,
  enquiryQuerySchema
} from '../validations/enquiry.validation';
import {
  getEnquiries,
  getEnquiry,
  createEn<PERSON>ry<PERSON><PERSON><PERSON>,
  closeEnquiryHandler,
} from '../controllers/enquiry.controller';

const router = Router();

/**
 * @swagger
 * /api/enquiries:
 *   get:
 *     summary: Get user's enquiries
 *     tags: [Enquiries]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, RESPONDED, CLOSED]
 *         description: Filter by enquiry status
 *     responses:
 *       200:
 *         description: Enquiries retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/', authenticateRider, validate(enquiryQuerySchema, 'query'), getEnquiries);

/**
 * @swagger
 * /api/enquiries/{id}:
 *   get:
 *     summary: Get enquiry by ID
 *     tags: [Enquiries]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Enquiry ID
 *     responses:
 *       200:
 *         description: Enquiry retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Not your enquiry
 *       404:
 *         description: Enquiry not found
 */
router.get('/:id', authenticateRider, getEnquiry);

/**
 * @swagger
 * /api/enquiries:
 *   post:
 *     summary: Create new enquiry
 *     tags: [Enquiries]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - subject
 *               - message
 *               - contactPreference
 *             properties:
 *               subject:
 *                 type: string
 *               message:
 *                 type: string
 *               contactPreference:
 *                 type: string
 *                 enum: [EMAIL, PHONE]
 *     responses:
 *       201:
 *         description: Enquiry created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.post('/', authenticateRider, validate(createEnquirySchema), createEnquiryHandler);

/**
 * @swagger
 * /api/enquiries/{id}/close:
 *   patch:
 *     summary: Close an enquiry
 *     tags: [Enquiries]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Enquiry ID
 *     responses:
 *       200:
 *         description: Enquiry closed successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Not your enquiry
 *       404:
 *         description: Enquiry not found
 */
router.patch('/:id/close', authenticateRider, closeEnquiryHandler);

export default router;
