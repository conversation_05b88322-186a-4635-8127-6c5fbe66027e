import { Router } from 'express';
import { authenticateRider } from '../middlewares/auth.middleware';
import validate from '../middlewares/validate.middleware';
import {
  createSupportQuerySchema,
  supportQueryQuerySchema,
  updateSupportQueryStatusSchema,
} from '../validations/supportQuery.validation';
import {
  getSupportQueries,
  getSupportQuery,
  createSupportQueryHandler,
  updateSupportQueryStatusHandler,
} from '../controllers/supportQuery.controller';

const router = Router();

// All routes require rider authentication
router.use(authenticateRider);

/**
 * @swagger
 * /api/support-queries:
 *   get:
 *     summary: Get user's support queries
 *     tags: [Support Queries]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, RESPONDED, CLOSED]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: Support queries retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/', validate(supportQueryQuerySchema, 'query'), getSupportQueries);

/**
 * @swagger
 * /api/support-queries/{id}:
 *   get:
 *     summary: Get support query by ID
 *     tags: [Support Queries]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Support query ID
 *     responses:
 *       200:
 *         description: Support query retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Support query not found
 */
router.get('/:id', getSupportQuery);

/**
 * @swagger
 * /api/support-queries:
 *   post:
 *     summary: Create new support query
 *     tags: [Support Queries]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - query
 *             properties:
 *               query:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 1000
 *                 description: Support query text
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Contact email (optional)
 *               phone:
 *                 type: string
 *                 description: Contact phone number (optional)
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *                 description: Contact name (optional)
 *     responses:
 *       201:
 *         description: Support query created successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 */
router.post('/', validate(createSupportQuerySchema), createSupportQueryHandler);

/**
 * @swagger
 * /api/support-queries/{id}/status:
 *   patch:
 *     summary: Update support query status
 *     tags: [Support Queries]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Support query ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [CLOSED]
 *                 description: New status (only CLOSED allowed for users)
 *     responses:
 *       200:
 *         description: Support query status updated successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Support query not found
 */
router.patch('/:id/status', validate(updateSupportQueryStatusSchema), updateSupportQueryStatusHandler);

export default router;
