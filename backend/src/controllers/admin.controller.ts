import { Request, Response, NextFunction } from 'express';
import prisma from '../config/database';
import { AppError, asyncHandler } from '../utils/errorHandler';
import { createNotification } from '../services/notification.service';
import { notifyUserRegistration } from '../services/adminNotification.service';
import { sendAccountApprovalEmail, sendAccountRejectionEmail } from '../services/email.service';

/**
 * Get all users with pagination and filtering
 */
export const getUsers = asyncHandler(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string;
  const isApproved = req.query.isApproved === 'true' ? true :
                     req.query.isApproved === 'false' ? false :
                     undefined;

  // Build where clause
  const where: any = {};

  // Add search functionality
  if (search && search.trim()) {
    where.OR = [
      { companyName: { contains: search, mode: 'insensitive' } },
      { contactPerson: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { gstNumber: { contains: search, mode: 'insensitive' } },
      { contactNumber: { contains: search, mode: 'insensitive' } },
    ];
  }

  if (isApproved !== undefined) {
    where.isApproved = isApproved;
  }

  // Get users with pagination
  const [users, total] = await Promise.all([
    prisma.rider.findMany({
      where,
      select: {
        id: true,
        gstNumber: true,
        companyName: true,
        contactPerson: true,
        contactNumber: true,
        email: true,
        paymentTerms: true,
        isApproved: true,
        approvedAt: true,
        rejectedAt: true,
        rejectionReason: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit,
    }),
    prisma.rider.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return res.status(200).json({
    status: 'success',
    data: {
      users,
      pagination: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage,
        hasPrevPage,
      },
    },
  });
});

/**
 * Approve a user
 */
export const approveUser = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  // Check if user exists
  const user = await prisma.rider.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      companyName: true,
      isApproved: true,
    },
  });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check if user is already approved
  if (user.isApproved) {
    return next(new AppError('User is already approved', 400));
  }

  // Approve user
  const approvedUser = await prisma.rider.update({
    where: { id },
    data: {
      isApproved: true,
      approvedAt: new Date(),
      rejectedAt: null,
      rejectionReason: null,
    },
  });

  // Send notification
  await createNotification(
    user.id,
    'ACCOUNT_APPROVAL',
    'Account Approved',
    'Your account has been approved. You can now place orders.'
  );

  // Send approval email
  try {
    await sendAccountApprovalEmail(user.email, user.companyName);
  } catch (error) {
    console.error('Error sending approval email:', error);
    // Continue even if email sending fails
  }

  return res.status(200).json({
    status: 'success',
    message: 'User approved successfully',
    data: { user: approvedUser },
  });
});

/**
 * Reject a user
 */
export const rejectUser = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const { rejectionReason } = req.body;

  if (!rejectionReason) {
    return next(new AppError('Rejection reason is required', 400));
  }

  // Check if user exists
  const user = await prisma.rider.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      companyName: true,
      isApproved: true,
    },
  });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Reject user
  const rejectedUser = await prisma.rider.update({
    where: { id },
    data: {
      isApproved: false,
      rejectedAt: new Date(),
      rejectionReason,
      approvedAt: null,
    },
  });

  // Send notification
  await createNotification(
    user.id,
    'ACCOUNT_APPROVAL',
    'Account Application Status',
    `Your account application has not been approved. Reason: ${rejectionReason}`
  );

  // Send rejection email
  try {
    await sendAccountRejectionEmail(user.email, user.companyName, rejectionReason);
  } catch (error) {
    console.error('Error sending rejection email:', error);
    // Continue even if email sending fails
  }

  return res.status(200).json({
    status: 'success',
    message: 'User rejected successfully',
    data: { user: rejectedUser },
  });
});

/**
 * Get user by ID
 */
export const getUserById = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  // Check if user exists
  const user = await prisma.rider.findUnique({
    where: { id },
    select: {
      id: true,
      gstNumber: true,
      companyName: true,
      contactPerson: true,
      contactNumber: true,
      email: true,
      paymentTerms: true,
      isApproved: true,
      approvedAt: true,
      rejectedAt: true,
      rejectionReason: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  return res.status(200).json({
    status: 'success',
    data: { user },
  });
});
