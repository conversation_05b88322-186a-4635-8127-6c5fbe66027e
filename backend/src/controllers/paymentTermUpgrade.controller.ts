import { Request, Response, NextFunction } from 'express';
import { AppError, asyncHandler } from '../utils/errorHandler';
import {
  getUserPaymentTermUpgradeRequests,
  getAllPaymentTermUpgradeRequests,
  getPaymentTermUpgradeRequestById,
  createPaymentTermUpgradeRequest,
  updatePaymentTermUpgradeRequestStatus,
} from '../services/paymentTermUpgrade.service';
import { RequestStatus } from '@prisma/client';

/**
 * Get user's payment term upgrade requests
 */
export const getPaymentTermUpgradeRequests = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const {
    page,
    limit,
    status,
    sortBy,
    sortOrder,
  } = req.query;

  const result = await getUserPaymentTermUpgradeRequests(userId, {
    page: page ? parseInt(page as string) : undefined,
    limit: limit ? parseInt(limit as string) : undefined,
    status: status as RequestStatus,
    sortBy: sortBy as string,
    sortOrder: sortOrder as 'asc' | 'desc',
  });

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Get all payment term upgrade requests (admin only)
 */
export const getAllPaymentTermUpgradeRequestsHandler = asyncHandler(async (req: Request, res: Response) => {
  const {
    page,
    limit,
    status,
    sortBy,
    sortOrder,
  } = req.query;

  const result = await getAllPaymentTermUpgradeRequests({
    page: page ? parseInt(page as string) : undefined,
    limit: limit ? parseInt(limit as string) : undefined,
    status: status as RequestStatus,
    sortBy: sortBy as string,
    sortOrder: sortOrder as 'asc' | 'desc',
  });

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Get payment term upgrade request by ID
 */
export const getPaymentTermUpgradeRequest = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user?.role === 'RIDER' ? req.user.id : undefined;

  const request = await getPaymentTermUpgradeRequestById(id, userId);

  return res.status(200).json({
    status: 'success',
    data: { request },
  });
});

/**
 * Create new payment term upgrade request
 */
export const createPaymentTermUpgradeRequestHandler = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const { reason, requestedPaymentTerms } = req.body;

  const request = await createPaymentTermUpgradeRequest(userId, {
    reason,
    requestedPaymentTerms,
  });

  return res.status(201).json({
    status: 'success',
    message: 'Payment term upgrade request created successfully',
    data: { request },
  });
});

/**
 * Update payment term upgrade request status (admin only)
 */
export const updatePaymentTermUpgradeRequestStatusHandler = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { status } = req.body;

  const request = await updatePaymentTermUpgradeRequestStatus(id, status);

  return res.status(200).json({
    status: 'success',
    message: `Payment term upgrade request ${status.toLowerCase()}`,
    data: { request },
  });
});
