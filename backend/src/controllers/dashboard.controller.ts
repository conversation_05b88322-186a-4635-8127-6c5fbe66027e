import { Request, Response } from 'express';
import { AppError, asyncHandler } from '../utils/errorHandler';
import { 
  getSalesOverview, 
  getTopSellingProducts, 
  getCustomerStats, 
  getInventoryStats 
} from '../services/analytics.service';
import { getCustomerLedger, syncInvoiceWithTally } from '../services/tally.service';

/**
 * Get dashboard statistics
 */
export const getDashboardStats = asyncHandler(async (req: Request, res: Response) => {
  const period = req.query.period as string || 'last30days';
  
  // Get all analytics data in parallel
  const [salesOverview, topProducts, customerStats, inventoryStats] = await Promise.all([
    getSalesOverview(period),
    getTopSellingProducts(5, period),
    getCustomerStats(period),
    getInventoryStats(),
  ]);

  return res.status(200).json({
    status: 'success',
    data: {
      salesOverview,
      topProducts,
      customerStats,
      inventoryStats,
    },
  });
});

/**
 * Get sales overview
 */
export const getSalesOverviewHandler = asyncHandler(async (req: Request, res: Response) => {
  const period = req.query.period as string || 'last30days';
  
  const salesOverview = await getSalesOverview(period);

  return res.status(200).json({
    status: 'success',
    data: salesOverview,
  });
});

/**
 * Get top selling products
 */
export const getTopSellingProductsHandler = asyncHandler(async (req: Request, res: Response) => {
  const period = req.query.period as string || 'last30days';
  const limit = parseInt(req.query.limit as string) || 5;
  
  const topProducts = await getTopSellingProducts(limit, period);

  return res.status(200).json({
    status: 'success',
    data: topProducts,
  });
});

/**
 * Get customer statistics
 */
export const getCustomerStatsHandler = asyncHandler(async (req: Request, res: Response) => {
  const period = req.query.period as string || 'last30days';
  
  const customerStats = await getCustomerStats(period);

  return res.status(200).json({
    status: 'success',
    data: customerStats,
  });
});

/**
 * Get inventory statistics
 */
export const getInventoryStatsHandler = asyncHandler(async (req: Request, res: Response) => {
  const inventoryStats = await getInventoryStats();

  return res.status(200).json({
    status: 'success',
    data: inventoryStats,
  });
});

/**
 * Get customer ledger from Tally
 */
export const getCustomerLedgerHandler = asyncHandler(async (req: Request, res: Response) => {
  const { gstNumber } = req.params;
  
  const ledgerData = await getCustomerLedger(gstNumber);

  return res.status(200).json({
    status: 'success',
    data: ledgerData,
  });
});

/**
 * Sync invoice with Tally
 */
export const syncInvoiceHandler = asyncHandler(async (req: Request, res: Response) => {
  const { orderId } = req.params;
  
  const syncResult = await syncInvoiceWithTally(orderId);

  return res.status(200).json({
    status: 'success',
    data: syncResult,
  });
});
