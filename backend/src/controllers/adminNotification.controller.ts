/**
 * Admin Notification Controller
 * Handles admin notification endpoints
 */

import { Request, Response } from 'express';
import { AppError, asyncHandler } from '../utils/errorHandler';
import {
  getAdminNotifications,
  markAdminNotificationAsRead,
  markAllAdminNotificationsAsRead,
  deleteAdminNotification,
  getUnreadAdminNotificationCount,
} from '../services/adminNotification.service';

/**
 * Get admin notifications with pagination and filters
 */
export const getAdminNotificationsHandler = asyncHandler(async (req: Request, res: Response) => {
  const { page, limit, unreadOnly, priority, category } = req.query;

  const result = await getAdminNotifications(
    page ? parseInt(page as string) : 1,
    limit ? parseInt(limit as string) : 10,
    unreadOnly === 'true',
    priority as any,
    category as any
  );

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Get unread admin notification count
 */
export const getUnreadAdminNotificationCountHandler = asyncHandler(async (req: Request, res: Response) => {
  const count = await getUnreadAdminNotificationCount();

  return res.status(200).json({
    status: 'success',
    data: { unreadCount: count },
  });
});

/**
 * Mark admin notification as read
 */
export const markAdminNotificationAsReadHandler = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Notification ID is required', 400);
  }

  await markAdminNotificationAsRead(id);

  return res.status(200).json({
    status: 'success',
    message: 'Notification marked as read successfully',
  });
});

/**
 * Mark all admin notifications as read
 */
export const markAllAdminNotificationsAsReadHandler = asyncHandler(async (req: Request, res: Response) => {
  const result = await markAllAdminNotificationsAsRead();

  return res.status(200).json({
    status: 'success',
    message: `${result.count} notifications marked as read`,
    data: result,
  });
});

/**
 * Delete admin notification
 */
export const deleteAdminNotificationHandler = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Notification ID is required', 400);
  }

  await deleteAdminNotification(id);

  return res.status(200).json({
    status: 'success',
    message: 'Notification deleted successfully',
  });
});
