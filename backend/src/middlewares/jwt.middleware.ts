import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AppError } from '../utils/errorHandler';
import prisma from '../config/database';

// Secret for JWT token
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Interface for JWT payload
interface JwtPayload {
  email: string;
  id: number | null;
  isVerified: boolean;
}

// Extend Express Request type
declare global {
  namespace Express {
    interface Request {
      rider?: {
        id: number | null;
        email: string;
        isVerified: boolean;
      };
    }
  }
}

/**
 * Middleware to authenticate JWT tokens
 */
export const authenticateJwt = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(new AppError('No token provided', 401));
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET) as JwtPayload;
    
    // Attach user to request
    req.user = {
      id: decoded.id.toString(),
      email: decoded.email,
      role: "RIDER"
    };
    
    next();
  } catch (error) {
    return next(new AppError('Invalid or expired token', 401));
  }
};

/**
 * Middleware to check if user is registered and approved
 */
export const requireRegisteredUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user || !req.user.id) {
      return next(new AppError('You must be registered to access this resource', 403));
    }
    
    // Check if user exists and is approved
    const rider = await prisma.rider.findUnique({
      where: { id: req.user.id },
      select: { isApproved: true }
    });
    
    if (!rider) {
      return next(new AppError('User not found', 404));
    }
    
    if (!rider.isApproved) {
      return next(new AppError('Your account is pending approval', 403));
    }
    
    next();
  } catch (error) {
    return next(new AppError('Authentication error', 500));
  }
};
