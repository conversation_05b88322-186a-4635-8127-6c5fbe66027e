import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

/**
 * Middleware to validate request data against a Joi schema
 * @param schema - Joi validation schema
 * @param property - Request property to validate (body, query, params)
 */
const validate = (schema: Joi.ObjectSchema, property: 'body' | 'query' | 'params' = 'body') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true,
    });

    if (!error) {
      next();
    } else {
      const errorDetails = error.details.map((detail) => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));

      res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errorDetails,
      });
    }
  };
};

export default validate;