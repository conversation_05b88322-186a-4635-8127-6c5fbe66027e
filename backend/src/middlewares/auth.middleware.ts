// filepath: /home/<USER>/Dev/project-kraft/backend/src/middlewares/auth.middleware.ts
import { Request, Response, NextFunction } from 'express';
import prisma from '../config/database';
import { verifyRiderToken, verifyAdminToken } from '../services/firebase.service';

// Extend Express Request interface to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email?: string;
        role: string;
        isAdminUser?: boolean;
      };
    }
  }
}

/**
 * Middleware to authenticate rider using Firebase token
 */
export const authenticateRider = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        status: 'error',
        message: 'Authentication required. Please login.',
      });
      return;
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      res.status(401).json({
        status: 'error',
        message: 'Authentication token is missing',
      });
      return;
    }

    // Verify Firebase token
    const { uid, email } = await verifyRiderToken(token);

    // Find rider in database using Firebase UID
    const rider = await prisma.rider.findFirst({
      where: { firebaseUid: uid },
      select: { id: true, isApproved: true, email: true },
    });

    if (!rider) {
      res.status(401).json({
        status: 'error',
        message: 'Rider not found or token is invalid',
      });
      return;
    }

    // Check if rider is approved
    if (!rider.isApproved) {
      res.status(403).json({
        status: 'error',
        message: 'Your account is pending approval',
      });
      return;
    }

    // Set rider in request object
    req.user = {
      id: rider.id,
      email: rider.email,
      role: 'RIDER',
      isAdminUser: false,
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({
      status: 'error',
      message: 'Invalid or expired token',
    });
  }
};

/**
 * Middleware to authenticate admin using Firebase token
 */
export const authenticateAdmin = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        status: 'error',
        message: 'Authentication required. Please login.',
      });
      return;
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      res.status(401).json({
        status: 'error',
        message: 'Authentication token is missing',
      });
      return;
    }

    // Verify Firebase admin token
    const { uid, email } = await verifyAdminToken(token);

    // Set admin user in request object with Firebase UID as ID
    req.user = {
      id: uid,
      email,
      role: 'ADMIN',
      isAdminUser: true,
    };

    next();
  } catch (error) {
    console.error('Admin authentication error:', error);
    res.status(401).json({
      status: 'error',
      message: 'Invalid or expired admin token',
    });
  }
};

/**
 * Middleware to check for admin role
 */
export const isAdmin = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user || req.user.role !== 'ADMIN') {
    res.status(403).json({
      status: 'error',
      message: 'Access denied. Admin privileges required.',
    });
    return;
  }
  next();
};

/**
 * Middleware to check for rider role
 */
export const isRider = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user || req.user.role !== 'RIDER') {
    res.status(403).json({
      status: 'error',
      message: 'Access denied. Rider privileges required.',
    });
    return;
  }
  next();
};

/**
 * Middleware to restrict access based on user role
 * @param roles - Array of allowed roles
 */
export const authorize = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        status: 'error',
        message: 'Authentication required',
      });
      return;
    }

    if (!roles.includes(req.user.role)) {
      res.status(403).json({
        status: 'error',
        message: 'You do not have permission to access this resource',
      });
      return;
    }

    next();
  };
};
