import prisma from '../config/database';

/**
 * Generate a unique enquiry number
 * Format: ENQ-YYYYMMDD-XXXX (where XXXX is a sequential number)
 * @returns Unique enquiry number
 */
export const generateEnquiryNumber = async (): Promise<string> => {
  // Get current date in YYYYMMDD format
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const dateStr = `${year}${month}${day}`;
  
  // Get prefix
  const prefix = `ENQ-${dateStr}-`;
  
  // Find the latest enquiry with this prefix
  const latestEnquiry = await prisma.enquiry.findFirst({
    where: {
      enquiryNumber: {
        startsWith: prefix,
      },
    },
    orderBy: {
      enquiryNumber: 'desc',
    },
  });
  
  let sequentialNumber = 1;
  
  if (latestEnquiry) {
    // Extract the sequential number from the latest enquiry number
    const latestSequentialStr = latestEnquiry.enquiryNumber.split('-')[2];
    const latestSequential = parseInt(latestSequentialStr, 10);
    
    // Increment the sequential number
    sequentialNumber = latestSequential + 1;
  }
  
  // Format the sequential number with leading zeros
  const sequentialStr = String(sequentialNumber).padStart(4, '0');
  
  // Combine to form the enquiry number
  return `${prefix}${sequentialStr}`;
};
