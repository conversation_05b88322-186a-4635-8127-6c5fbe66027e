import prisma from '../config/database';

/**
 * Generate a unique order number
 * Format: KP-YYYYMMDD-XXXX (where XXXX is a sequential number)
 * @returns Unique order number
 */
export const generateOrderNumber = async (): Promise<string> => {
  // Get current date in YYYYMMDD format
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const dateStr = `${year}${month}${day}`;
  
  // Get prefix
  const prefix = `KP-${dateStr}-`;
  
  // Find the latest order with this prefix
  const latestOrder = await prisma.order.findFirst({
    where: {
      orderNumber: {
        startsWith: prefix,
      },
    },
    orderBy: {
      orderNumber: 'desc',
    },
  });
  
  let sequentialNumber = 1;
  
  if (latestOrder) {
    // Extract the sequential number from the latest order number
    const latestSequentialStr = latestOrder.orderNumber.split('-')[2];
    const latestSequential = parseInt(latestSequentialStr, 10);
    
    // Increment the sequential number
    sequentialNumber = latestSequential + 1;
  }
  
  // Format the sequential number with leading zeros
  const sequentialStr = String(sequentialNumber).padStart(4, '0');
  
  // Combine to form the order number
  return `${prefix}${sequentialStr}`;
};
