import { Request, Response, NextFunction } from 'express';

/**
 * Custom error class with status code
 */
export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Async handler to catch errors in async route handlers
 * @param fn - Async route handler function
 */
export const asyncHandler = (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Global error handler middleware
 */
export const errorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  err.statusCode = err.statusCode || 500;
  err.message = err.message || 'Internal Server Error';

  // Prisma error handling
  if (err.code === 'P2002') {
    err.statusCode = 409;
    err.message = `Duplicate value for ${err.meta?.target?.join(', ')}`;
  }

  // JWT error handling
  if (err.name === 'JsonWebTokenError') {
    err.statusCode = 401;
    err.message = 'Invalid token. Please log in again.';
  }

  if (err.name === 'TokenExpiredError') {
    err.statusCode = 401;
    err.message = 'Your token has expired. Please log in again.';
  }

  // Development error response (with stack trace)
  if (process.env.NODE_ENV === 'development') {
    return res.status(err.statusCode).json({
      status: 'error',
      message: err.message,
      stack: err.stack,
      error: err,
    });
  }

  // Production error response (without sensitive error details)
  const isOperationalError = err.isOperational;
  if (!isOperationalError) {
    // Log non-operational errors for debugging
    console.error('ERROR 💥', err);
    
    // Generic message for programming or unknown errors
    err.message = 'Something went wrong';
  }

  return res.status(err.statusCode).json({
    status: 'error',
    message: err.message,
  });
};
