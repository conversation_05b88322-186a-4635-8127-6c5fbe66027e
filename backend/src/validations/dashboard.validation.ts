import Joi from 'joi';

// Dashboard query validation schema
export const dashboardQuerySchema = Joi.object({
  period: Joi.string()
    .valid('last7days', 'last30days', 'last90days', 'lastYear', 'allTime')
    .messages({
      'string.base': 'Period must be a string',
      'any.only': 'Period must be one of: last7days, last30days, last90days, lastYear, allTime',
    }),
});

// Top products query validation schema
export const topProductsQuerySchema = Joi.object({
  period: Joi.string()
    .valid('last7days', 'last30days', 'last90days', 'lastYear', 'allTime')
    .messages({
      'string.base': 'Period must be a string',
      'any.only': 'Period must be one of: last7days, last30days, last90days, lastYear, allTime',
    }),
  limit: Joi.number().integer().min(1).max(50)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 50',
    }),
});

// Customer ledger validation schema
export const customerLedgerParamSchema = Joi.object({
  gstNumber: Joi.string().required()
    .pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/)
    .messages({
      'string.empty': 'GST number is required',
      'string.pattern.base': 'Invalid GST number format',
      'any.required': 'GST number is required',
    }),
});

// Sync invoice validation schema
export const syncInvoiceParamSchema = Joi.object({
  orderId: Joi.string().required()
    .messages({
      'string.empty': 'Order ID is required',
      'any.required': 'Order ID is required',
    }),
});
