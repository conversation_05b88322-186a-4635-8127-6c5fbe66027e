import Joi from 'joi';

// User rejection validation schema
export const rejectUserSchema = Joi.object({
  rejectionReason: Joi.string().min(5).max(500).required()
    .messages({
      'string.min': 'Rejection reason must be at least 5 characters long',
      'string.max': 'Rejection reason cannot exceed 500 characters',
      'any.required': 'Rejection reason is required',
    }),
});

// User query validation schema
export const userQuerySchema = Joi.object({
  page: Joi.number().integer().min(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number().integer().min(1).max(100)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  isApproved: Joi.boolean()
    .messages({
      'boolean.base': 'Approval status must be a boolean',
    }),
});

// Order status update validation schema
export const orderStatusUpdateSchema = Joi.object({
  status: Joi.string().valid('PENDING', 'APPROVED', 'SHIPPED', 'DELIVERED', 'CANCELLED').required()
    .messages({
      'any.only': 'Status must be one of: PENDING, APPROVED, SHIPPED, DELIVERED, CANCELLED',
      'any.required': 'Status is required',
    }),
});

// Enquiry response validation schema
export const enquiryResponseSchema = Joi.object({
  response: Joi.string().min(5).max(1000).required()
    .messages({
      'string.min': 'Response must be at least 5 characters long',
      'string.max': 'Response cannot exceed 1000 characters',
      'any.required': 'Response is required',
    }),
});
