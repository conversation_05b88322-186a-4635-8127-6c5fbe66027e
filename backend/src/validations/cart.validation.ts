import Joi from 'joi';

// Add to cart validation schema
export const addToCartSchema = Joi.object({
  stockId: Joi.string().uuid().required()
    .messages({
      'string.guid': 'Stock ID must be a valid UUID',
      'any.required': 'Stock ID is required',
    }),
  quantity: Joi.number().integer().min(1).required()
    .messages({
      'number.base': 'Quantity must be a number',
      'number.integer': 'Quantity must be an integer',
      'number.min': 'Quantity must be at least 1',
      'any.required': 'Quantity is required',
    }),
});

// Update cart item validation schema
export const updateCartItemSchema = Joi.object({
  quantity: Joi.number().integer().min(1).required()
    .messages({
      'number.base': 'Quantity must be a number',
      'number.integer': 'Quantity must be an integer',
      'number.min': 'Quantity must be at least 1',
      'any.required': 'Quantity is required',
    }),
});

// Cart query validation schema
export const cartQuerySchema = Joi.object({
  includeStock: Joi.boolean()
    .messages({
      'boolean.base': 'Include stock must be a boolean',
    }),
});
