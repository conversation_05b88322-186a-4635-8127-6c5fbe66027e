import Joi from 'joi';

// Payment term upgrade request creation validation schema
export const createPaymentTermUpgradeRequestSchema = Joi.object({
  reason: Joi.string().trim().min(10).max(500).required()
    .messages({
      'string.empty': 'Reason is required',
      'string.min': 'Reason must be at least 10 characters long',
      'string.max': 'Reason cannot exceed 500 characters',
      'any.required': 'Reason is required',
    }),
  requestedPaymentTerms: Joi.string().valid('THIRTY_DAYS', 'SIXTY_DAYS').required()
    .messages({
      'any.only': 'Requested payment terms must be either THIRTY_DAYS or SIXTY_DAYS',
      'any.required': 'Requested payment terms is required',
    }),
});

// Payment term upgrade request query validation schema
export const paymentTermUpgradeQuerySchema = Joi.object({
  page: Joi.number().integer().min(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number().integer().min(1).max(100)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  status: Joi.string().valid('PENDING', 'APPROVED', 'REJECTED')
    .messages({
      'any.only': 'Status must be one of: PENDING, APPROVED, REJECTED',
    }),
  sortBy: Joi.string().valid('createdAt', 'updatedAt', 'status')
    .messages({
      'any.only': 'Sort by must be one of: createdAt, updatedAt, status',
    }),
  sortOrder: Joi.string().valid('asc', 'desc')
    .messages({
      'any.only': 'Sort order must be one of: asc, desc',
    }),
});

// Payment term upgrade request status update validation schema (admin only)
export const updatePaymentTermUpgradeStatusSchema = Joi.object({
  status: Joi.string().valid('APPROVED', 'REJECTED').required()
    .messages({
      'any.only': 'Status must be either APPROVED or REJECTED',
      'any.required': 'Status is required',
    }),
});
