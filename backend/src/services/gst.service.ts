import axios from 'axios';
import { AppError } from '../utils/errorHandler';

/**
 * Validate GST number format
 * @param gstNumber - GST number to validate
 * @returns Boolean indicating if the format is valid
 */
export const validateGSTFormat = (gstNumber: string): boolean => {
  // GST number format: 2 digits, 5 characters, 4 digits, 1 character, 1 character, Z, 1 character
  const gstPattern = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
  return gstPattern.test(gstNumber);
};

/**
 * Validate GST number with external API (if available)
 * In a production environment, this would connect to the GST verification API
 * For development, we'll just validate the format
 * @param gstNumber - GST number to validate
 * @returns Object containing validation result and business details
 */
export const validateGST = async (gstNumber: string): Promise<{
  valid: boolean;
  businessName?: string;
  address?: string;
  error?: string;
}> => {
  // First validate the format
  if (!validateGSTFormat(gstNumber)) {
    return {
      valid: false,
      error: 'Invalid GST number format',
    };
  }

  // In development mode, just return success with mock data
  if (process.env.NODE_ENV === 'development' || !process.env.GST_API_KEY) {
    // Extract state code from GST number (first two digits)
    const stateCode = gstNumber.substring(0, 2);
    
    // Mock business name based on GST number
    const businessName = `Business ${gstNumber.substring(5, 9)}`;
    
    return {
      valid: true,
      businessName,
      address: `123 Business Street, State ${stateCode}, India`,
    };
  }

  // In production, connect to GST verification API
  try {
    // This is a placeholder for the actual API call
    // Replace with the actual GST verification API endpoint and authentication
    const response = await axios.get(`https://api.gst.gov.in/validate/${gstNumber}`, {
      headers: {
        'Authorization': `Bearer ${process.env.GST_API_KEY}`,
      },
    });

    // Check if the GST number is valid
    if (response.data.valid) {
      return {
        valid: true,
        businessName: response.data.businessName,
        address: response.data.address,
      };
    } else {
      return {
        valid: false,
        error: response.data.error || 'Invalid GST number',
      };
    }
  } catch (error) {
    console.error('Error validating GST number:', error);
    
    // If the API call fails, fall back to format validation
    return {
      valid: true, // Assume valid if format is correct but API fails
      businessName: `Business (Verification API Unavailable)`,
      address: 'Address not available',
    };
  }
};

/**
 * Extract state code from GST number
 * @param gstNumber - GST number
 * @returns State code (first two digits of GST number)
 */
export const getStateCodeFromGST = (gstNumber: string): string => {
  if (!validateGSTFormat(gstNumber)) {
    throw new AppError('Invalid GST number format', 400);
  }
  
  return gstNumber.substring(0, 2);
};

/**
 * Get state name from state code
 * @param stateCode - State code (first two digits of GST number)
 * @returns State name
 */
export const getStateNameFromCode = (stateCode: string): string => {
  const stateCodes: Record<string, string> = {
    '01': 'Jammu and Kashmir',
    '02': 'Himachal Pradesh',
    '03': 'Punjab',
    '04': 'Chandigarh',
    '05': 'Uttarakhand',
    '06': 'Haryana',
    '07': 'Delhi',
    '08': 'Rajasthan',
    '09': 'Uttar Pradesh',
    '10': 'Bihar',
    '11': 'Sikkim',
    '12': 'Arunachal Pradesh',
    '13': 'Nagaland',
    '14': 'Manipur',
    '15': 'Mizoram',
    '16': 'Tripura',
    '17': 'Meghalaya',
    '18': 'Assam',
    '19': 'West Bengal',
    '20': 'Jharkhand',
    '21': 'Odisha',
    '22': 'Chhattisgarh',
    '23': 'Madhya Pradesh',
    '24': 'Gujarat',
    '26': 'Dadra and Nagar Haveli and Daman and Diu',
    '27': 'Maharashtra',
    '28': 'Andhra Pradesh',
    '29': 'Karnataka',
    '30': 'Goa',
    '31': 'Lakshadweep',
    '32': 'Kerala',
    '33': 'Tamil Nadu',
    '34': 'Puducherry',
    '35': 'Andaman and Nicobar Islands',
    '36': 'Telangana',
    '37': 'Andhra Pradesh (New)',
    '38': 'Ladakh',
  };

  return stateCodes[stateCode] || 'Unknown State';
};
