/**
 * Admin Notification Service
 * Handles all admin-specific notifications
 */

import prisma from '../config/database';
import { AdminNotificationType, NotificationPriority, NotificationCategory } from '../generated/prisma';

export interface CreateAdminNotificationData {
  type: AdminNotificationType;
  title: string;
  message: string;
  priority?: NotificationPriority;
  category?: NotificationCategory;
  metadata?: any;
}

/**
 * Create a new admin notification
 */
export const createAdminNotification = async (data: CreateAdminNotificationData) => {
  try {
    const notification = await prisma.adminNotification.create({
      data: {
        type: data.type,
        title: data.title,
        message: data.message,
        priority: data.priority || 'NORMAL',
        category: data.category || 'SYSTEM',
        metadata: data.metadata || null,
      },
    });

    console.log('Admin notification created:', notification.id);
    return notification;
  } catch (error) {
    console.error('Error creating admin notification:', error);
    throw error;
  }
};

/**
 * Get admin notifications with pagination
 */
export const getAdminNotifications = async (
  page = 1,
  limit = 10,
  unreadOnly = false,
  priority?: NotificationPriority,
  category?: NotificationCategory
) => {
  const skip = (page - 1) * limit;

  const where: any = {};
  
  if (unreadOnly) {
    where.isRead = false;
  }
  
  if (priority) {
    where.priority = priority;
  }
  
  if (category) {
    where.category = category;
  }

  const [notifications, total] = await Promise.all([
    prisma.adminNotification.findMany({
      where,
      orderBy: [
        { priority: 'desc' }, // Show urgent notifications first
        { createdAt: 'desc' }
      ],
      skip,
      take: limit,
    }),
    prisma.adminNotification.count({ where }),
  ]);

  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    notifications,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};

/**
 * Mark admin notification as read
 */
export const markAdminNotificationAsRead = async (id: string) => {
  try {
    const notification = await prisma.adminNotification.update({
      where: { id },
      data: { 
        isRead: true,
        updatedAt: new Date(),
      },
    });

    return notification;
  } catch (error) {
    console.error('Error marking admin notification as read:', error);
    throw error;
  }
};

/**
 * Mark all admin notifications as read
 */
export const markAllAdminNotificationsAsRead = async () => {
  try {
    const result = await prisma.adminNotification.updateMany({
      where: { isRead: false },
      data: { 
        isRead: true,
        updatedAt: new Date(),
      },
    });

    return result;
  } catch (error) {
    console.error('Error marking all admin notifications as read:', error);
    throw error;
  }
};

/**
 * Delete admin notification
 */
export const deleteAdminNotification = async (id: string) => {
  try {
    await prisma.adminNotification.delete({
      where: { id },
    });

    return true;
  } catch (error) {
    console.error('Error deleting admin notification:', error);
    throw error;
  }
};

/**
 * Get unread admin notification count
 */
export const getUnreadAdminNotificationCount = async () => {
  try {
    const count = await prisma.adminNotification.count({
      where: { isRead: false },
    });

    return count;
  } catch (error) {
    console.error('Error getting unread admin notification count:', error);
    return 0;
  }
};

/**
 * Helper functions to create specific types of admin notifications
 */

export const notifyUserRegistration = async (userData: any) => {
  return createAdminNotification({
    type: 'USER_REGISTRATION',
    title: 'New User Registration',
    message: `New user ${userData.companyName} (${userData.email}) has registered and is pending approval.`,
    priority: 'NORMAL',
    category: 'USER_ACTION',
    metadata: { userId: userData.id, email: userData.email },
  });
};

export const notifyOrderPlaced = async (orderData: any) => {
  return createAdminNotification({
    type: 'ORDER_PLACED',
    title: 'New Order Placed',
    message: `Order #${orderData.orderNumber} placed by ${orderData.companyName} for ₹${orderData.totalAmount}.`,
    priority: 'HIGH',
    category: 'ORDER',
    metadata: { orderId: orderData.id, userId: orderData.userId, amount: orderData.totalAmount },
  });
};

export const notifyBulkNotificationSent = async (count: number, title: string) => {
  return createAdminNotification({
    type: 'BULK_NOTIFICATION_SENT',
    title: 'Bulk Notification Sent',
    message: `Promotional notification "${title}" sent to ${count} users successfully.`,
    priority: 'LOW',
    category: 'SYSTEM',
    metadata: { recipientCount: count, notificationTitle: title },
  });
};

export const notifyEnquirySubmitted = async (enquiryData: any) => {
  return createAdminNotification({
    type: 'ENQUIRY_SUBMITTED',
    title: 'New Enquiry Submitted',
    message: `New enquiry submitted by ${enquiryData.companyName} for ${enquiryData.productName}.`,
    priority: 'NORMAL',
    category: 'ENQUIRY',
    metadata: { enquiryId: enquiryData.id, userId: enquiryData.userId },
  });
};

export const notifyPaymentReceived = async (paymentData: any) => {
  return createAdminNotification({
    type: 'PAYMENT_RECEIVED',
    title: 'Payment Received',
    message: `Payment of ₹${paymentData.amount} received for order #${paymentData.orderNumber}.`,
    priority: 'HIGH',
    category: 'PAYMENT',
    metadata: { orderId: paymentData.orderId, amount: paymentData.amount },
  });
};

export const notifySystemError = async (error: string, context: string) => {
  return createAdminNotification({
    type: 'SYSTEM_ERROR',
    title: 'System Error Detected',
    message: `System error in ${context}: ${error}`,
    priority: 'URGENT',
    category: 'SYSTEM',
    metadata: { error, context, timestamp: new Date().toISOString() },
  });
};
