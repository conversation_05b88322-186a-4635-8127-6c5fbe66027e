import prisma from '../config/database';
import redisClient from '../config/redis';
import { OrderStatus, PaymentStatus } from '@prisma/client';

// Cache TTL in seconds (15 minutes)
const CACHE_TTL = 900;

/**
 * Get sales overview analytics
 * @param period - Time period for analytics (last7days, last30days, last90days, lastYear, allTime)
 * @returns Sales overview analytics
 */
export const getSalesOverview = async (period: string = 'last30days') => {
  // Create cache key
  const cacheKey = `analytics:sales:${period}`;

  // Try to get from cache
  const cachedData = await redisClient.get(cacheKey);
  if (cachedData) {
    return JSON.parse(cachedData.toString());
  }

  // Calculate date range based on period
  const endDate = new Date();
  let startDate = new Date();

  switch (period) {
    case 'last7days':
      startDate.setDate(endDate.getDate() - 7);
      break;
    case 'last30days':
      startDate.setDate(endDate.getDate() - 30);
      break;
    case 'last90days':
      startDate.setDate(endDate.getDate() - 90);
      break;
    case 'lastYear':
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    case 'allTime':
      startDate = new Date(0); // Beginning of time
      break;
    default:
      startDate.setDate(endDate.getDate() - 30); // Default to last 30 days
  }

  // Get total sales amount
  const totalSales = await prisma.order.aggregate({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      status: {
        not: OrderStatus.CANCELLED,
      },
    },
    _sum: {
      totalAmount: true,
    },
  });

  // Get total number of orders
  const totalOrders = await prisma.order.count({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      status: {
        not: OrderStatus.CANCELLED,
      },
    },
  });

  // Get total number of delivered orders
  const deliveredOrders = await prisma.order.count({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      status: OrderStatus.DELIVERED,
    },
  });

  // Get total number of pending payments
  const pendingPayments = await prisma.order.count({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      paymentStatus: PaymentStatus.PENDING,
    },
  });

  // Get total amount of pending payments
  const pendingPaymentsAmount = await prisma.order.aggregate({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      paymentStatus: PaymentStatus.PENDING,
    },
    _sum: {
      totalAmount: true,
    },
  });

  // Calculate average order value
  const averageOrderValue = totalOrders > 0
    ? (totalSales._sum.totalAmount || 0) / totalOrders
    : 0;

  // Calculate delivery rate
  const deliveryRate = totalOrders > 0
    ? (deliveredOrders / totalOrders) * 100
    : 0;

  const result = {
    totalSales: totalSales._sum.totalAmount || 0,
    totalOrders,
    deliveredOrders,
    pendingPayments,
    pendingPaymentsAmount: pendingPaymentsAmount._sum.totalAmount || 0,
    averageOrderValue,
    deliveryRate,
    period,
  };

  // Cache the result
  await redisClient.set(cacheKey, JSON.stringify(result), {
    EX: CACHE_TTL,
  });

  return result;
};

/**
 * Get top selling products
 * @param limit - Number of top products to return
 * @param period - Time period for analytics (last7days, last30days, last90days, lastYear, allTime)
 * @returns Top selling products
 */
export const getTopSellingProducts = async (limit: number = 5, period: string = 'last30days') => {
  // Create cache key
  const cacheKey = `analytics:topProducts:${period}:${limit}`;

  // Try to get from cache
  const cachedData = await redisClient.get(cacheKey);
  if (cachedData) {
    return JSON.parse(cachedData.toString());
  }

  // Calculate date range based on period
  const endDate = new Date();
  let startDate = new Date();

  switch (period) {
    case 'last7days':
      startDate.setDate(endDate.getDate() - 7);
      break;
    case 'last30days':
      startDate.setDate(endDate.getDate() - 30);
      break;
    case 'last90days':
      startDate.setDate(endDate.getDate() - 90);
      break;
    case 'lastYear':
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    case 'allTime':
      startDate = new Date(0); // Beginning of time
      break;
    default:
      startDate.setDate(endDate.getDate() - 30); // Default to last 30 days
  }

  // Get top selling products
  const topProducts = await prisma.orderItem.groupBy({
    by: ['type', 'gsm', 'bf'],
    where: {
      order: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        status: {
          not: OrderStatus.CANCELLED,
        },
      },
    },
    _sum: {
      quantity: true,
      totalPrice: true,
    },
    orderBy: {
      _sum: {
        quantity: 'desc',
      },
    },
    take: limit,
  });

  // Format the result
  const result = topProducts.map(product => ({
    type: product.type,
    gsm: product.gsm,
    bf: product.bf,
    totalQuantity: product._sum.quantity || 0,
    totalRevenue: product._sum.totalPrice || 0,
  }));

  // Cache the result
  await redisClient.set(cacheKey, JSON.stringify(result), {
    EX: CACHE_TTL,
  });

  return result;
};

/**
 * Get customer statistics
 * @param period - Time period for analytics (last7days, last30days, last90days, lastYear, allTime)
 * @returns Customer statistics
 */
export const getCustomerStats = async (period: string = 'last30days') => {
  // Create cache key
  const cacheKey = `analytics:customers:${period}`;

  // Try to get from cache
  const cachedData = await redisClient.get(cacheKey);
  if (cachedData) {
    return JSON.parse(cachedData.toString());
  }

  // Calculate date range based on period
  const endDate = new Date();
  let startDate = new Date();

  switch (period) {
    case 'last7days':
      startDate.setDate(endDate.getDate() - 7);
      break;
    case 'last30days':
      startDate.setDate(endDate.getDate() - 30);
      break;
    case 'last90days':
      startDate.setDate(endDate.getDate() - 90);
      break;
    case 'lastYear':
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    case 'allTime':
      startDate = new Date(0); // Beginning of time
      break;
    default:
      startDate.setDate(endDate.getDate() - 30); // Default to last 30 days
  }

  // Get total number of customers
  const totalCustomers = await prisma.rider.count({
    where: {
      isApproved: true,
    },
  });

  // Get new customers in the period
  const newCustomers = await prisma.rider.count({
    where: {
      isApproved: true,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
  });

  // Get active customers (placed at least one order in the period)
  const activeCustomers = await prisma.order.groupBy({
    by: ['userId'],
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    _count: true,
  });

  // Get top customers by order value
  const topCustomers = await prisma.order.groupBy({
    by: ['userId'],
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      status: {
        not: OrderStatus.CANCELLED,
      },
    },
    _sum: {
      totalAmount: true,
    },
    orderBy: {
      _sum: {
        totalAmount: 'desc',
      },
    },
    take: 5,
  });

  // Get customer details for top customers
  const topCustomersWithDetails = await Promise.all(
    topCustomers.map(async (customer) => {
      const userDetails = await prisma.rider.findUnique({
        where: { id: customer.userId },
        select: {
          id: true,
          companyName: true,
          contactPerson: true,
          email: true,
        },
      });

      return {
        ...userDetails,
        totalSpent: customer._sum.totalAmount || 0,
      };
    })
  );

  const result = {
    totalCustomers,
    newCustomers,
    activeCustomers: activeCustomers.length,
    topCustomers: topCustomersWithDetails,
    period,
  };

  // Cache the result
  await redisClient.set(cacheKey, JSON.stringify(result), {
    EX: CACHE_TTL,
  });

  return result;
};

/**
 * Get inventory statistics
 * @returns Inventory statistics
 */
export const getInventoryStats = async () => {
  // Create cache key
  const cacheKey = 'analytics:inventory';

  // Try to get from cache
  const cachedData = await redisClient.get(cacheKey);
  if (cachedData) {
    return JSON.parse(cachedData.toString());
  }

  // Get total number of stock items
  const totalStockItems = await prisma.stock.count();

  // Get total inventory value
  const inventoryValue = await prisma.stock.aggregate({
    _sum: {
      sixtyDayPrice: true,
    },
  });

  // Get low stock items (less than 10 rolls available)
  const lowStockItems = await prisma.stock.findMany({
    where: {
      rollsAvailable: {
        lt: 10,
      },
    },
    orderBy: {
      rollsAvailable: 'asc',
    },
  });

  // Get out of stock items
  const outOfStockItems = await prisma.stock.count({
    where: {
      rollsAvailable: 0,
    },
  });

  const result = {
    totalStockItems,
    inventoryValue: inventoryValue._sum.sixtyDayPrice || 0,
    lowStockItems,
    outOfStockItems,
  };

  // Cache the result
  await redisClient.set(cacheKey, JSON.stringify(result), {
    EX: CACHE_TTL,
  });

  return result;
};
