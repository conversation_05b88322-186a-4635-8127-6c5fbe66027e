import nodemailer from 'nodemailer';
import { AppError } from '../utils/errorHandler';

// Create a test account for development if no SMTP credentials are provided
let transporter: nodemailer.Transporter;

const initializeTransporter = async () => {
  if (process.env.NODE_ENV === 'development' && !process.env.SMTP_HOST) {
    // Create a test account for development
    const testAccount = await nodemailer.createTestAccount();

    transporter = nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: testAccount.user,
        pass: testAccount.pass,
      },
    });

    console.log('Using Ethereal test account for email:', testAccount.user);
  } else {
    // Use provided SMTP credentials
    transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }
};

// Initialize the transporter
initializeTransporter();

/**
 * Send an email
 * @param to - Recipient email address
 * @param subject - Email subject
 * @param html - Email HTML content
 * @returns Information about the sent email
 */
export const sendEmail = async (
  to: string,
  subject: string,
  html: string
): Promise<{ messageId: string; previewUrl?: string }> => {
  try {
    // Ensure transporter is initialized
    if (!transporter) {
      await initializeTransporter();
    }

    const mailOptions = {
      from: `"Kraft Paper App" <${process.env.SMTP_FROM || '<EMAIL>'}>`,
      to,
      subject,
      html,
    };

    const info = await transporter.sendMail(mailOptions);

    // For development, log the preview URL
    if (process.env.NODE_ENV === 'development') {
      console.log('Email preview URL:', nodemailer.getTestMessageUrl(info));
    }

    return {
      messageId: info.messageId,
      previewUrl: process.env.NODE_ENV === 'development' ? nodemailer.getTestMessageUrl(info) as string : undefined,
    };
  } catch (error) {
    console.error('Error sending email:', error);
    throw new AppError('Failed to send email', 500);
  }
};

/**
 * Send OTP email
 * @param to - Recipient email address
 * @param otp - OTP code
 * @returns Information about the sent email
 */
export const sendOTPEmail = async (
  to: string,
  otp: string
): Promise<{ messageId: string; previewUrl?: string }> => {
  const subject = 'Your OTP for Kraft Paper App';
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Verification Code</h2>
      <p>Your OTP for Kraft Paper App is:</p>
      <div style="background-color: #f4f4f4; padding: 10px; text-align: center; font-size: 24px; letter-spacing: 5px; font-weight: bold;">
        ${otp}
      </div>
      <p>This code will expire in 10 minutes.</p>
      <p>If you didn't request this code, please ignore this email.</p>
    </div>
  `;

  return sendEmail(to, subject, html);
};

/**
 * Send password reset email
 * @param to - Recipient email address
 * @param resetToken - Password reset token
 * @param resetUrl - Password reset URL
 * @returns Information about the sent email
 */
export const sendPasswordResetEmail = async (
  to: string,
  resetToken: string,
  resetUrl: string
): Promise<{ messageId: string; previewUrl?: string }> => {
  const subject = 'Password Reset for Kraft Paper App';
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Password Reset</h2>
      <p>You requested a password reset for your Kraft Paper App account.</p>
      <p>Click the button below to reset your password:</p>
      <div style="text-align: center; margin: 20px 0;">
        <a href="${resetUrl}?token=${resetToken}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a>
      </div>
      <p>Or copy and paste this link in your browser:</p>
      <p>${resetUrl}?token=${resetToken}</p>
      <p>This link will expire in 1 hour.</p>
      <p>If you didn't request a password reset, please ignore this email.</p>
    </div>
  `;

  return sendEmail(to, subject, html);
};

/**
 * Send account approval notification
 * @param to - Recipient email address
 * @param companyName - Company name
 * @returns Information about the sent email
 */
export const sendAccountApprovalEmail = async (
  to: string,
  companyName: string
): Promise<{ messageId: string; previewUrl?: string }> => {
  const subject = 'Your Account has been Approved - Kraft Paper App';
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Account Approved</h2>
      <p>Congratulations! Your account for ${companyName} has been approved.</p>
      <p>You can now log in to the Kraft Paper App and start placing orders.</p>
      <div style="text-align: center; margin: 20px 0;">
        <a href="${process.env.FRONTEND_URL || 'https://kraftpaper.com'}/login" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login to Your Account</a>
      </div>
      <p>Thank you for choosing Kraft Paper App for your business needs.</p>
    </div>
  `;

  return sendEmail(to, subject, html);
};

/**
 * Send account rejection notification
 * @param to - Recipient email address
 * @param companyName - Company name
 * @param reason - Rejection reason
 * @returns Information about the sent email
 */
export const sendAccountRejectionEmail = async (
  to: string,
  companyName: string,
  reason: string
): Promise<{ messageId: string; previewUrl?: string }> => {
  const subject = 'Account Application Status - Kraft Paper App';
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Account Application Status</h2>
      <p>We regret to inform you that your account application for ${companyName} has not been approved at this time.</p>
      <p><strong>Reason:</strong> ${reason}</p>
      <p>If you believe this is an error or would like to provide additional information, please contact our support team.</p>
    </div>
  `;

  return sendEmail(to, subject, html);
};

/**
 * Send enquiry response email
 * @param to - Recipient email address
 * @param enquiryNumber - Enquiry number
 * @param subject - Enquiry subject
 * @param response - Response message
 * @returns Information about the sent email
 */
export const sendEnquiryResponseEmail = async (
  to: string,
  enquiryNumber: string,
  subject: string,
  response: string
): Promise<{ messageId: string; previewUrl?: string }> => {
  const emailSubject = `Response to Your Enquiry #${enquiryNumber}`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Response to Your Enquiry</h2>
      <p>We have responded to your enquiry #${enquiryNumber} regarding "${subject}".</p>
      <div style="background-color: #f4f4f4; padding: 15px; margin: 15px 0; border-left: 4px solid #4CAF50;">
        <p><strong>Our Response:</strong></p>
        <p>${response}</p>
      </div>
      <p>If you have any further questions, please feel free to create a new enquiry.</p>
      <p>Thank you for choosing Kraft Paper App for your business needs.</p>
    </div>
  `;

  return sendEmail(to, emailSubject, html);
};

/**
 * Send new enquiry notification email to admin
 * @param to - Admin email address
 * @param enquiryNumber - Enquiry number
 * @param companyName - Company name
 * @param subject - Enquiry subject
 * @returns Information about the sent email
 */
export const sendNewEnquiryNotificationEmail = async (
  to: string,
  enquiryNumber: string,
  companyName: string,
  subject: string
): Promise<{ messageId: string; previewUrl?: string }> => {
  const emailSubject = `New Enquiry #${enquiryNumber} Received`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>New Enquiry Received</h2>
      <p>A new enquiry has been received from ${companyName}.</p>
      <div style="background-color: #f4f4f4; padding: 15px; margin: 15px 0;">
        <p><strong>Enquiry Number:</strong> ${enquiryNumber}</p>
        <p><strong>Subject:</strong> ${subject}</p>
      </div>
      <p>Please log in to the admin dashboard to view and respond to this enquiry.</p>
    </div>
  `;

  return sendEmail(to, emailSubject, html);
};

export default {
  sendEmail,
  sendOTPEmail,
  sendPasswordResetEmail,
  sendAccountApprovalEmail,
  sendAccountRejectionEmail,
  sendEnquiryResponseEmail,
  sendNewEnquiryNotificationEmail,
};