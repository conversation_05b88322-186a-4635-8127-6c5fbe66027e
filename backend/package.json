{"name": "kraft", "version": "1.0.0", "description": "kraft mobile app backend", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "nodemon --exec tsx app.ts", "build": "tsc", "postinstall": "prisma generate", "test": "jest", "test:watch": "jest --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\" \"*.ts\"", "prisma:generate": "prisma generate", "prisma:studio": "prisma studio", "prisma:seed": "tsx prisma/seed.ts", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "docker:build": "docker build -t kraft-backend .", "docker:run": "docker run -p 5000:5000 kraft-backend", "docker:dev": "docker-compose up --build", "docker:prod": "docker-compose -f docker-compose.yml up -d"}, "repository": {"type": "git", "url": "git+https://github.com/harshit3478/kraft-backend.git"}, "keywords": ["kraft", "backend", "nodejs", "typescript", "express", "prisma"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/harshit3478/kraft-backend/issues"}, "homepage": "https://github.com/harshit3478/kraft-backend#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.11", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/xml2js": "^0.4.14", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.4.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "prisma": "^6.7.0", "supertest": "^6.3.4", "ts-jest": "^29.3.2", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "dependencies": {"@prisma/client": "^6.7.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "nodemailer": "^7.0.2", "redis": "^5.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "xml2js": "^0.6.2"}}