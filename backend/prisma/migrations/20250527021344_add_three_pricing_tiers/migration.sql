/*
  Migration to add three pricing tiers to Stock table

  This migration:
  1. Adds three new price columns with temporary default values
  2. Migrates existing pricePerRoll data to the new columns
  3. Removes the old pricePerRoll column
  4. Removes default values from new columns
*/

-- Step 1: Add new price columns with temporary default values
ALTER TABLE "Stock"
ADD COLUMN "immediatePrice" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN "thirtyDayPrice" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN "sixtyDayPrice" DOUBLE PRECISION NOT NULL DEFAULT 0;

-- Step 2: Migrate existing data from pricePerRoll to new columns
-- For existing data, we'll use the following pricing strategy:
-- - immediatePrice = pricePerRoll (same as current price)
-- - thirtyDayPrice = pricePerRoll * 1.05 (5% markup for 30-day terms)
-- - sixtyDayPrice = pricePerRoll * 1.10 (10% markup for 60-day terms)
UPDATE "Stock"
SET
  "immediatePrice" = "pricePerRoll",
  "thirtyDayPrice" = "pricePerRoll" * 1.05,
  "sixtyDayPrice" = "pricePerRoll" * 1.10
WHERE "pricePerRoll" IS NOT NULL;

-- Step 3: Drop the old pricePerRoll column
ALTER TABLE "Stock" DROP COLUMN "pricePerRoll";

-- Step 4: Remove default values from new columns (making them truly required)
ALTER TABLE "Stock"
ALTER COLUMN "immediatePrice" DROP DEFAULT,
ALTER COLUMN "thirtyDayPrice" DROP DEFAULT,
ALTER COLUMN "sixtyDayPrice" DROP DEFAULT;
