name: Deploy to EC2

on:
  push:
    branches: [ "main", "deve" ]
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy to EC2
    runs-on: ubuntu-latest
    
    steps:
    - name: Deploy to EC2 via SSH
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ${{ secrets.EC2_USER }}
        key: ${{ secrets.EC2_SSH_KEY }}
        port: ${{ secrets.EC2_PORT || 22 }}
        script: |
          set -e  # Exit on any error
          
          echo "🚀 Starting deployment..."
          
          # Navigate to app directory
          cd /opt/kraft-backend
          
          # Pull latest code
          echo "📥 Pulling latest code..."
          git pull origin main
          
          # Update environment file
          echo "🔧 Updating environment variables..."
          cat > .env << 'EOF'
          NODE_ENV=production
          PORT=5000
          DATABASE_URL=${{ secrets.PROD_DATABASE_URL }}
          REDIS_URL=redis://redis:6379
          
          # Firebase configs
          RIDER_FIREBASE_PROJECT_ID=${{ secrets.RIDER_FIREBASE_PROJECT_ID }}
          RIDER_FIREBASE_PRIVATE_KEY="${{ secrets.RIDER_FIREBASE_PRIVATE_KEY }}"
          RIDER_FIREBASE_CLIENT_EMAIL=${{ secrets.RIDER_FIREBASE_CLIENT_EMAIL }}
          RIDER_FIREBASE_API_KEY=${{ secrets.RIDER_FIREBASE_API_KEY }}
          
          ADMIN_FIREBASE_PROJECT_ID=${{ secrets.ADMIN_FIREBASE_PROJECT_ID }}
          ADMIN_FIREBASE_PRIVATE_KEY="${{ secrets.ADMIN_FIREBASE_PRIVATE_KEY }}"
          ADMIN_FIREBASE_CLIENT_EMAIL=${{ secrets.ADMIN_FIREBASE_CLIENT_EMAIL }}
          ADMIN_FIREBASE_API_KEY=${{ secrets.ADMIN_FIREBASE_API_KEY }}
          
          # Email config
          SMTP_HOST=${{ secrets.SMTP_HOST }}
          SMTP_PORT=${{ secrets.SMTP_PORT }}
          SMTP_SECURE=${{ secrets.SMTP_SECURE }}
          SMTP_USER=${{ secrets.SMTP_USER }}
          SMTP_PASS=${{ secrets.SMTP_PASS }}
          SMTP_FROM=${{ secrets.SMTP_FROM }}
          
          # Other configs
          JWT_SECRET=${{ secrets.PROD_JWT_SECRET }}
          JWT_EXPIRES_IN=${{ secrets.PROD_JWT_EXPIRES_IN }}
          FRONTEND_URL=${{ secrets.PROD_FRONTEND_URL }}
          DEFAULT_ADMIN_EMAIL=${{ secrets.DEFAULT_ADMIN_EMAIL }}
          GST_API_KEY=${{ secrets.GST_API_KEY }}
          EOF
          
          # Build and restart services
          echo "🔨 Building and starting services..."
          docker-compose down
          docker-compose build --no-cache app
          docker-compose up -d
          
          # Wait for services to be ready
          echo "⏳ Waiting for services to start..."
          sleep 10
          
          # Check if app is running
          if curl -f http://localhost:5000/health; then
            echo "✅ Deployment successful! App is running."
          else
            echo "❌ Deployment failed! App is not responding."
            docker-compose logs app
            exit 1
          fi
          
          # Clean up old docker images
          echo "🧹 Cleaning up old images..."
          docker image prune -f
          
          echo "🎉 Deployment completed successfully!"