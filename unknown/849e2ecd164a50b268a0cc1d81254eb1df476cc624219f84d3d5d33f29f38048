"use client"

import type React from "react"
import { create<PERSON>ontext, useContext, useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { authService } from "@/lib/services/authService"
import type { User } from "@/lib/types"
import analyticsService from "@/lib/analytics/analytics-service"

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  sendPasswordResetEmail: (email: string) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const router = useRouter()

  // Check if user is already logged in using Firebase auth state listener
  useEffect(() => {
    const unsubscribe = authService.onAuthStateChanged(async (firebaseUser) => {
      setIsLoading(true)
      try {
        if (firebaseUser) {
          // Convert Firebase user to our User interface
          const userData = authService.mapFirebaseUserToUser(firebaseUser)
          setUser(userData)

          // Store token for API requests
          const token = await authService.getIdToken(true)
          if (token) {
            localStorage.setItem("auth_token", token)
          }

          // Track user session
          analyticsService.init(userData.uid)
          analyticsService.trackEvent("user_session_restored", "app", undefined, {
            userId: userData.uid,
          })
        } else {
          // No user, clear state
          setUser(null)
          localStorage.removeItem("auth_token")
        }
      } catch (error) {
        console.error("Auth state change error:", error)
        setUser(null)
        localStorage.removeItem("auth_token")
      } finally {
        setIsLoading(false)
      }
    })

    // Cleanup subscription on unmount
    return () => unsubscribe()
  }, [])

  // Login function
  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      console.log("Attempting login with Firebase:", email)

      // Sign in with Firebase
      const userCredential = await authService.signIn({ email, password })
      const firebaseUser = userCredential.user

      // Convert to our User interface
      const userData = authService.mapFirebaseUserToUser(firebaseUser)
      setUser(userData)

      // Store token
      const token = await authService.getIdToken(true)
      if (token) {
        localStorage.setItem("auth_token", token)
      }

      // Track login event
      analyticsService.init(userData.uid)
      analyticsService.trackEvent("user_login", "login_page", undefined, { userId: userData.uid })

      toast.success("Login successful")

      // Navigate to dashboard
      router.replace("/dashboard")
    } catch (error) {
      console.error("Login error:", error)
      toast.error("Login failed. Please check your credentials.")
      analyticsService.trackError("Login failed")
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Logout function
  const logout = async () => {
    setIsLoading(true)
    try {
      // Track logout event
      if (user) {
        analyticsService.trackEvent("user_logout", "app", undefined, { userId: user.uid })
      }

      // Sign out from Firebase
      await authService.signOut()

      // Clear user state
      setUser(null)
      localStorage.removeItem("auth_token")

      toast.success("Logout successful")

      // Navigate to login page
      router.replace("/login")
    } catch (error) {
      console.error("Logout error:", error)
      toast.error("Logout failed")
      analyticsService.trackError("Logout failed")
    } finally {
      setIsLoading(false)
    }
  }

  // Send password reset email
  const sendPasswordResetEmail = async (email: string) => {
    try {
      await authService.sendPasswordResetEmail(email)
      toast.success("Password reset email sent. Please check your inbox.")
      analyticsService.trackEvent("password_reset_request", "login_page", undefined, { email })
    } catch (error) {
      console.error("Password reset error:", error)
      toast.error("Failed to send password reset email. Please try again.")
      analyticsService.trackError("Password reset failed")
      throw error
    }
  }

  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    sendPasswordResetEmail,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
