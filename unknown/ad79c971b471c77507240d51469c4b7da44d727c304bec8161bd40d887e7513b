"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YA<PERSON><PERSON> } from "@/components/ui/chart"

// Default data for fallback
const defaultData = [
  {
    name: "<PERSON>",
    revenue: 18000,
    orders: 25,
  },
  {
    name: "Feb",
    revenue: 22000,
    orders: 35,
  },
  {
    name: "<PERSON>",
    revenue: 32000,
    orders: 45,
  },
  {
    name: "Apr",
    revenue: 28000,
    orders: 30,
  },
  {
    name: "May",
    revenue: 45000,
    orders: 50,
  },
  {
    name: "<PERSON>",
    revenue: 52000,
    orders: 65,
  },
  {
    name: "Jul",
    revenue: 48000,
    orders: 60,
  },
  {
    name: "Aug",
    revenue: 55000,
    orders: 70,
  },
  {
    name: "Sep",
    revenue: 60000,
    orders: 80,
  },
  {
    name: "Oct",
    revenue: 68000,
    orders: 85,
  },
  {
    name: "Nov",
    revenue: 72000,
    orders: 90,
  },
  {
    name: "Dec",
    revenue: 80000,
    orders: 100,
  },
]

interface MonthlySales {
  month: string;
  revenue: number;
  orders: number;
}

interface OverviewProps {
  data?: MonthlySales[] | null;
}

export function Overview({ data }: OverviewProps) {
  // Use provided data or fallback to default
  const chartData = data || defaultData;
  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="month" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
        <YAxis
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `₹${value}`}
        />
        <Tooltip formatter={(value) => `₹${value.toLocaleString()}`} />
        <Legend />
        <Bar dataKey="revenue" name="Revenue" fill="#adfa1d" radius={[4, 4, 0, 0]} />
        <Bar dataKey="orders" name="Orders" fill="#1e40af" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}
