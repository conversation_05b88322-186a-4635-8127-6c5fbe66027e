import apiClient from "./apiClient"

export interface Notification {
  id: string
  title: string
  message: string
  read: boolean
  createdAt: string
  type: "order" | "stock" | "inquiry" | "system" | "ACCOUNT_APPROVAL" | "ORDER_STATUS_UPDATE" | "ENQUIRY_RESPONSE" | "STOCK_ALERT" | "SYSTEM_ALERT" | "STOCK_LOW" | "PAYMENT_RECEIVED" | "ORDER_PLACED" | "USER_APPROVED" | "ENQUIRY_SUBMITTED" | "USER_REGISTRATION" | "BULK_NOTIFICATION_SENT"
  link?: string
  isRead?: boolean
  priority?: string
  category?: string
  metadata?: any
}

interface NotificationsResponse {
  notifications: Notification[]
  unreadCount: number
  total?: number
  page?: number
  limit?: number
}

interface BackendNotification {
  id: string
  title: string
  message: string
  isRead: boolean
  createdAt: string
  type: string
  priority?: string
  category?: string
  metadata?: any
}

interface BackendResponse {
  status: string
  data: {
    notifications: BackendNotification[]
    pagination: {
      total: number
      page: number
      limit: number
      totalPages: number
      hasNextPage: boolean
      hasPrevPage: boolean
    }
  }
}

// Transform backend notification to frontend format
const transformNotification = (backendNotification: BackendNotification): Notification => {
  const validTypes = [
    "order", "stock", "inquiry", "system", "ACCOUNT_APPROVAL", "ORDER_STATUS_UPDATE", 
    "ENQUIRY_RESPONSE", "STOCK_ALERT", "SYSTEM_ALERT", "STOCK_LOW", "PAYMENT_RECEIVED", 
    "ORDER_PLACED", "USER_APPROVED", "ENQUIRY_SUBMITTED", "USER_REGISTRATION", "BULK_NOTIFICATION_SENT"
  ] as const;
  
  const notificationType = validTypes.includes(backendNotification.type as any) 
    ? backendNotification.type as Notification['type']
    : "system";
    
  return {
    id: backendNotification.id,
    title: backendNotification.title,
    message: backendNotification.message,
    read: backendNotification.isRead || false,
    createdAt: backendNotification.createdAt,
    type: notificationType,
    isRead: backendNotification.isRead || false,
    priority: backendNotification.priority,
    category: backendNotification.category,
    metadata: backendNotification.metadata,
  }
}

export const NotificationService = {
  getNotifications: async (page = 1, limit = 10, unreadOnly = false): Promise<NotificationsResponse> => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(unreadOnly && { unreadOnly: 'true' })
      })

      const response = await apiClient.get(`/admin-notifications?${params}`)
      console.log("notifications response:", response)
      
      // The apiClient already returns the parsed response, so response itself is the BackendResponse
      const backendResponse = response as unknown as BackendResponse;

      if (backendResponse?.status === 'success') {
        const notifications = backendResponse.data.notifications.map(transformNotification)
        console.log("transformed notifications:", notifications)
        return {
          notifications,
          unreadCount: notifications.filter((n: Notification) => !n.read).length,
          total: backendResponse.data.pagination.total,
          page: backendResponse.data.pagination.page,
          limit: backendResponse.data.pagination.limit,
        }
      }

      throw new Error('Invalid response format')
    } catch (error) {
      console.error("Error fetching notifications:", error)
      // Return empty data on error instead of throwing
      return {
        notifications: [],
        unreadCount: 0,
        total: 0,
        page: 1,
        limit: 10,
      }
    }
  },

  markAsRead: async (id: string): Promise<void> => {
    try {
      await apiClient.patch(`/admin-notifications/${id}/read`, {})
    } catch (error) {
      console.error("Error marking notification as read:", error)
      throw error
    }
  },

  markAllAsRead: async (): Promise<void> => {
    try {
      await apiClient.patch('/admin-notifications/read-all', {})
    } catch (error) {
      console.error("Error marking all notifications as read:", error)
      throw error
    }
  },

  deleteNotification: async (id: string): Promise<void> => {
    try {
      await apiClient.delete(`/admin-notifications/${id}`)
    } catch (error) {
      console.error("Error deleting notification:", error)
      throw error
    }
  },

  sendBulkNotification: async (title: string, message: string): Promise<any> => {
    try {
      const response = await apiClient.post('/notifications/bulk-send', {
        title,
        message
      })
      console.log("Bulk notification response:", response)
      return response
    } catch (error) {
      console.error("Error sending bulk notification:", error)
      throw error
    }
  },
}

export default NotificationService
