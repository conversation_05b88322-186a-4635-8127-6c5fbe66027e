import apiClient from './apiClient';

export interface PaymentTermUpgradeRequest {
  id: string;
  riderId: string;
  rider: {
    companyName: string;
    contactPerson: string;
    email: string;
    gstNumber: string;
    currentPaymentTerms: string;
  };
  requestedPaymentTerm: string;
  message?: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  adminResponse?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentTermUpgradeFilters {
  status?: 'PENDING' | 'APPROVED' | 'REJECTED';
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface UpdatePaymentTermUpgradeStatusData {
  status: 'APPROVED' | 'REJECTED';
  adminResponse?: string;
}

class PaymentTermUpgradeService {
  /**
   * Get all payment term upgrade requests
   */
  async getAllRequests(filters?: PaymentTermUpgradeFilters) {
    try {
      const params = new URLSearchParams();
      
      if (filters?.status) {
        params.append('status', filters.status);
      }
      if (filters?.page) {
        params.append('page', filters.page.toString());
      }
      if (filters?.limit) {
        params.append('limit', filters.limit.toString());
      }
      if (filters?.sortBy) {
        params.append('sortBy', filters.sortBy);
      }
      if (filters?.sortOrder) {
        params.append('sortOrder', filters.sortOrder);
      }

      const queryString = params.toString();
      const url = `/admin/payment-term-upgrades${queryString ? `?${queryString}` : ''}`;
      
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching payment term upgrade requests:', error);
      throw error;
    }
  }

  /**
   * Get a specific payment term upgrade request by ID
   */
  async getRequestById(id: string) {
    try {
      const response = await apiClient.get(`/admin/payment-term-upgrades/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching payment term upgrade request:', error);
      throw error;
    }
  }

  /**
   * Update payment term upgrade request status
   */
  async updateStatus(id: string, data: UpdatePaymentTermUpgradeStatusData) {
    try {
      const response = await apiClient.put(`/admin/payment-term-upgrades/${id}/status`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating payment term upgrade status:', error);
      throw error;
    }
  }

  /**
   * Get payment term upgrade statistics
   */
  async getStatistics() {
    try {
      const response = await apiClient.get('/admin/payment-term-upgrades/statistics');
      return response.data;
    } catch (error) {
      console.error('Error fetching payment term upgrade statistics:', error);
      throw error;
    }
  }

  /**
   * Export payment term upgrade requests to CSV
   */
  async exportToCSV(filters?: PaymentTermUpgradeFilters) {
    try {
      const params = new URLSearchParams();
      
      if (filters?.status) {
        params.append('status', filters.status);
      }
      if (filters?.sortBy) {
        params.append('sortBy', filters.sortBy);
      }
      if (filters?.sortOrder) {
        params.append('sortOrder', filters.sortOrder);
      }

      const queryString = params.toString();
      const url = `/admin/payment-term-upgrades/export${queryString ? `?${queryString}` : ''}`;
      
      const response = await apiClient.get(url, {
        responseType: 'blob',
      });
      
      // Create blob and download
      const blob = new Blob([response.data], { type: 'text/csv' });
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `payment-term-upgrades-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
      
      return { success: true };
    } catch (error) {
      console.error('Error exporting payment term upgrade requests:', error);
      throw error;
    }
  }

  /**
   * Bulk update payment term upgrade requests
   */
  async bulkUpdate(ids: string[], data: UpdatePaymentTermUpgradeStatusData) {
    try {
      const response = await apiClient.put('/admin/payment-term-upgrades/bulk-update', {
        ids,
        ...data
      });
      return response.data;
    } catch (error) {
      console.error('Error bulk updating payment term upgrade requests:', error);
      throw error;
    }
  }

  /**
   * Get payment term upgrade request history for a specific rider
   */
  async getRiderHistory(riderId: string) {
    try {
      const response = await apiClient.get(`/admin/payment-term-upgrades/rider/${riderId}/history`);
      return response.data;
    } catch (error) {
      console.error('Error fetching rider payment term upgrade history:', error);
      throw error;
    }
  }
}

export const paymentTermUpgradeService = new PaymentTermUpgradeService();
export default paymentTermUpgradeService;
