import apiClient, { ApiResponse } from './apiClient';

// Types
export type OrderStatus = 'PENDING' | 'APPROVED' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';
export type PaymentStatus = 'PENDING' | 'PAID' | 'OVERDUE';
export type PaymentTerms = 'IMMEDIATE' | 'THIRTY_DAYS' | 'SIXTY_DAYS';
export type FulfillmentType = 'STANDARD_SHIPPING' | 'SELF_PICKUP';

export interface OrderItem {
  id: string;
  orderId: string;
  stockId: string;
  type: string;
  gsm: number;
  bf: number;
  width: number;
  quantity: number;
  pricePerRoll: number;
  totalPrice: number;
  stock: {
    type: string;
    gsm: number;
    bf: number;
  };
}

export interface ShippingAddress {
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface Rider {
  id: string;
  companyName: string;
  contactPerson: string;
  email: string;
  contactNumber: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  subtotalAmount: number;
  gstAmount: number;
  totalAmount: number;
  status: OrderStatus;
  paymentTerms: PaymentTerms;
  paymentStatus: PaymentStatus;
  paymentDueDate: string | null;
  fulfillmentType: FulfillmentType;
  shippingAddress: ShippingAddress | null;
  vehicleNumber: string | null;
  notes: string;
  createdAt: string;
  updatedAt: string;
  approvedAt: string | null;
  shippedAt: string | null;
  deliveredAt: string | null;
  cancelledAt: string | null;
  rider: Rider;
  items: OrderItem[];
}

export interface OrderQueryParams {
  page?: number;
  limit?: number;
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export interface UpdateOrderStatusDto {
  status: OrderStatus;
  notes?: string;
}

export interface UpdatePaymentStatusDto {
  paymentStatus: PaymentStatus;
  notes?: string;
}

/**
 * Order Service
 */
const orderService = {
  /**
   * Get all orders with pagination and filtering
   */
  async getAllOrders(params: OrderQueryParams = {}): Promise<ApiResponse<{
    orders: Order[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  }>> {
    return await apiClient.get<{
      orders: Order[];
      pagination: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
      };
    }>('/admin/orders', params);
  },

  /**
   * Get an order by ID
   */
  async getOrder(id: string): Promise<Order> {
    const response = await apiClient.get<{ order: Order }>(`/admin/orders/${id}`);
    return response.data.order;
  },

  /**
   * Update order status
   */
  async updateOrderStatus(id: string, data: UpdateOrderStatusDto): Promise<Order> {
    const response = await apiClient.patch<{ order: Order }>(`/admin/orders/${id}/status`, data);
    return response.data.order;
  },

  /**
   * Update payment status
   */
  async updatePaymentStatus(id: string, data: UpdatePaymentStatusDto): Promise<Order> {
    const response = await apiClient.patch<{ order: Order }>(`/admin/orders/${id}/payment-status`, data);
    return response.data.order;
  },
};

export default orderService;
