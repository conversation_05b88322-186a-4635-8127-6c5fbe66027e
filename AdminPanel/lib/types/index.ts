/**
 * Common types used across the application
 */

// Pagination types
export interface Pagination {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface PaginatedResponse<T> {
  data: T;
  pagination: Pagination;
}

// Query parameters
export interface BaseQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// API Response
export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

// User types
export type UserRole = 'ADMIN' | 'VENDOR';
export type PaymentTerms = 'IMMEDIATE' | 'THIRTY_DAYS' | 'SIXTY_DAYS';

// Unified User interface for authentication and business logic
export interface User {
  // Firebase Auth fields
  uid: string;
  email: string;
  displayName?: string;

  // Business fields (optional for admin users)
  id?: string;
  gstNumber?: string;
  companyName?: string;
  contactPerson?: string;
  contactNumber?: string;
  role?: UserRole;
  paymentTerms?: PaymentTerms;
  isApproved?: boolean;
  approvedAt?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  createdAt?: string;
  updatedAt?: string;
  orderCount?: number;
  totalSpend?: number;
}

// Authentication specific types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface UserQueryParams extends BaseQueryParams {
  isApproved?: boolean;
}

export interface RejectUserDto {
  rejectionReason: string;
}

// Inquiry types
export type InquiryStatus = 'PENDING' | 'RESPONDED' | 'CLOSED';
export type ContactPreference = 'EMAIL' | 'PHONE';

export interface Inquiry {
  id: string;
  enquiryNumber: string;
  userId: string;
  type: string;
  gsm: string;
  bf: string;
  width: string;
  quantity: number;
  message?: string;
  status: InquiryStatus;
  contactPreference: ContactPreference;
  response?: string;
  respondedAt?: string;
  respondedBy?: string;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    companyName: string;
    contactPerson: string;
    email: string;
    contactNumber: string;
  };
  // Legacy fields for backward compatibility
  subject?: string;
  companyName?: string;
}

export interface InquiryQueryParams extends BaseQueryParams {
  status?: InquiryStatus;
  startDate?: string;
  endDate?: string;
}

export interface RespondToInquiryDto {
  response: string;
}

// Order types
export type OrderStatus = 'PENDING' | 'APPROVED' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';
export type PaymentStatus = 'PENDING' | 'PAID' | 'OVERDUE';

// Notification types
export type NotificationType = 'ORDER_STATUS' | 'ENQUIRY_RESPONSE' | 'ACCOUNT_APPROVAL' | 'STOCK_UPDATE' | 'PAYMENT_REMINDER';

export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  readAt?: string;
}

export interface NotificationQueryParams extends BaseQueryParams {
  isRead?: boolean;
}
