"use client";

import { useState } from "react";
import { toast } from "sonner";
import { DashboardHeader } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/dashboard-shell";
import { But<PERSON> } from "@/components/ui/button";

export default function ToastTestPage() {
  const showSuccessToast = () => {
    toast.success("This is a success toast message");
  };

  const showErrorToast = () => {
    toast.error("This is an error toast message");
  };

  const showInfoToast = () => {
    toast("This is a default toast message");
  };

  return (
    <DashboardShell>
      <DashboardHeader heading="Toast Test" text="Test the toast functionality">
        <div></div>
      </DashboardHeader>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Button onClick={showSuccessToast} className="w-full">Show Success Toast</Button>
        <Button onClick={showErrorToast} variant="destructive" className="w-full">Show Error Toast</Button>
        <Button onClick={showInfoToast} variant="outline" className="w-full">Show Info Toast</Button>
      </div>
    </DashboardShell>
  );
}
