"use client";

import { useEffect, useState } from "react";
import { DashboardHeader } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/dashboard-shell";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  RefreshCw, 
  Eye,
  MessageSquare 
} from "lucide-react";
import { toast } from "sonner";
import { paymentTermUpgradeService } from "@/lib/api";

interface PaymentTermUpgrade {
  id: string;
  riderId: string;
  rider: {
    companyName: string;
    contactPerson: string;
    email: string;
    gstNumber: string;
    currentPaymentTerms: string;
  };
  requestedPaymentTerm: string;
  message?: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  adminResponse?: string;
  createdAt: string;
  updatedAt: string;
}

export default function PaymentTermUpgradesPage() {
  const [upgrades, setUpgrades] = useState<PaymentTermUpgrade[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUpgrade, setSelectedUpgrade] = useState<PaymentTermUpgrade | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject' | 'view'>('view');
  const [adminResponse, setAdminResponse] = useState('');
  const [processing, setProcessing] = useState(false);

  // Fetch payment term upgrades
  const fetchUpgrades = async () => {
    setLoading(true);
    try {
      const response = await paymentTermUpgradeService.getAllRequests();
      if (response?.data?.requests) {
        setUpgrades(response.data.requests);
      }
    } catch (error) {
      console.error('Error fetching payment term upgrades:', error);
      toast.error('Failed to load payment term upgrade requests');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUpgrades();
  }, []);

  // Handle approve/reject action
  const handleAction = async () => {
    if (!selectedUpgrade || actionType === 'view') return;

    setProcessing(true);
    try {
      await paymentTermUpgradeService.updateStatus(selectedUpgrade.id, {
        status: actionType === 'approve' ? 'APPROVED' : 'REJECTED',
        adminResponse: adminResponse.trim() || undefined
      });

      toast.success(`Payment term upgrade ${actionType === 'approve' ? 'approved' : 'rejected'} successfully`);
      
      // Refresh the list
      await fetchUpgrades();
      
      // Close dialog
      setDialogOpen(false);
      setSelectedUpgrade(null);
      setAdminResponse('');
    } catch (error) {
      console.error(`Error ${actionType}ing payment term upgrade:`, error);
      toast.error(`Failed to ${actionType} payment term upgrade`);
    } finally {
      setProcessing(false);
    }
  };

  // Open dialog for action
  const openDialog = (upgrade: PaymentTermUpgrade, action: 'approve' | 'reject' | 'view') => {
    setSelectedUpgrade(upgrade);
    setActionType(action);
    setAdminResponse(upgrade.adminResponse || '');
    setDialogOpen(true);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'APPROVED':
        return <Badge variant="outline" className="text-green-600 border-green-600"><CheckCircle className="w-3 h-3 mr-1" />Approved</Badge>;
      case 'REJECTED':
        return <Badge variant="outline" className="text-red-600 border-red-600"><XCircle className="w-3 h-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get payment term label
  const getPaymentTermLabel = (term: string) => {
    switch (term) {
      case 'IMMEDIATE':
        return 'Immediate';
      case '30_DAYS':
        return '30 Days';
      case '60_DAYS':
        return '60 Days';
      default:
        return term;
    }
  };

  // Filter upgrades by status
  const pendingUpgrades = upgrades.filter(u => u.status === 'PENDING');
  const processedUpgrades = upgrades.filter(u => u.status !== 'PENDING');

  return (
    <DashboardShell>
      <DashboardHeader heading="Payment Term Upgrades" text="Manage customer payment term upgrade requests">
        <Button variant="outline" size="icon" onClick={fetchUpgrades} disabled={loading}>
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </DashboardHeader>

      <Tabs defaultValue="pending" className="space-y-4">
        <TabsList>
          <TabsTrigger value="pending">
            Pending ({pendingUpgrades.length})
          </TabsTrigger>
          <TabsTrigger value="processed">
            Processed ({processedUpgrades.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Requests</CardTitle>
              <CardDescription>
                Payment term upgrade requests awaiting your review
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading requests...</span>
                </div>
              ) : pendingUpgrades.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No pending payment term upgrade requests
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Company</TableHead>
                      <TableHead>Current Terms</TableHead>
                      <TableHead>Requested Terms</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pendingUpgrades.map((upgrade) => (
                      <TableRow key={upgrade.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{upgrade.rider.companyName}</div>
                            <div className="text-sm text-muted-foreground">{upgrade.rider.contactPerson}</div>
                          </div>
                        </TableCell>
                        <TableCell>{getPaymentTermLabel(upgrade.rider.currentPaymentTerms)}</TableCell>
                        <TableCell>{getPaymentTermLabel(upgrade.requestedPaymentTerm)}</TableCell>
                        <TableCell>{new Date(upgrade.createdAt).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openDialog(upgrade, 'view')}
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              View
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openDialog(upgrade, 'approve')}
                              className="text-green-600 border-green-600 hover:bg-green-50"
                            >
                              <CheckCircle className="w-4 h-4 mr-1" />
                              Approve
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openDialog(upgrade, 'reject')}
                              className="text-red-600 border-red-600 hover:bg-red-50"
                            >
                              <XCircle className="w-4 h-4 mr-1" />
                              Reject
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="processed" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Processed Requests</CardTitle>
              <CardDescription>
                Previously approved or rejected payment term upgrade requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              {processedUpgrades.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No processed payment term upgrade requests
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Company</TableHead>
                      <TableHead>Current Terms</TableHead>
                      <TableHead>Requested Terms</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {processedUpgrades.map((upgrade) => (
                      <TableRow key={upgrade.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{upgrade.rider.companyName}</div>
                            <div className="text-sm text-muted-foreground">{upgrade.rider.contactPerson}</div>
                          </div>
                        </TableCell>
                        <TableCell>{getPaymentTermLabel(upgrade.rider.currentPaymentTerms)}</TableCell>
                        <TableCell>{getPaymentTermLabel(upgrade.requestedPaymentTerm)}</TableCell>
                        <TableCell>{getStatusBadge(upgrade.status)}</TableCell>
                        <TableCell>{new Date(upgrade.updatedAt).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDialog(upgrade, 'view')}
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {actionType === 'view' ? 'View Request' : 
               actionType === 'approve' ? 'Approve Request' : 'Reject Request'}
            </DialogTitle>
            <DialogDescription>
              {selectedUpgrade && (
                <>Payment term upgrade request from {selectedUpgrade.rider.companyName}</>
              )}
            </DialogDescription>
          </DialogHeader>
          
          {selectedUpgrade && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Company</Label>
                  <p className="text-sm">{selectedUpgrade.rider.companyName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Contact Person</Label>
                  <p className="text-sm">{selectedUpgrade.rider.contactPerson}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Current Payment Terms</Label>
                  <p className="text-sm">{getPaymentTermLabel(selectedUpgrade.rider.currentPaymentTerms)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Requested Payment Terms</Label>
                  <p className="text-sm">{getPaymentTermLabel(selectedUpgrade.requestedPaymentTerm)}</p>
                </div>
              </div>
              
              {selectedUpgrade.message && (
                <div>
                  <Label className="text-sm font-medium">Customer Message</Label>
                  <p className="text-sm bg-muted p-3 rounded-md">{selectedUpgrade.message}</p>
                </div>
              )}

              {actionType !== 'view' && (
                <div>
                  <Label htmlFor="adminResponse" className="text-sm font-medium">
                    Admin Response {actionType === 'reject' ? '(Required)' : '(Optional)'}
                  </Label>
                  <Textarea
                    id="adminResponse"
                    value={adminResponse}
                    onChange={(e) => setAdminResponse(e.target.value)}
                    placeholder={`Enter your response for ${actionType}ing this request...`}
                    className="mt-1"
                  />
                </div>
              )}

              {selectedUpgrade.adminResponse && actionType === 'view' && (
                <div>
                  <Label className="text-sm font-medium">Admin Response</Label>
                  <p className="text-sm bg-muted p-3 rounded-md">{selectedUpgrade.adminResponse}</p>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            {actionType !== 'view' && (
              <Button 
                onClick={handleAction} 
                disabled={processing || (actionType === 'reject' && !adminResponse.trim())}
                className={actionType === 'approve' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
              >
                {processing ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : actionType === 'approve' ? (
                  <CheckCircle className="w-4 h-4 mr-2" />
                ) : (
                  <XCircle className="w-4 h-4 mr-2" />
                )}
                {actionType === 'approve' ? 'Approve' : 'Reject'}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardShell>
  );
}
