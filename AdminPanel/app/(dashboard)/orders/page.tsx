"use client"

import type React from "react"

import { useState, useEffect, useCallback } from "react"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Search, RefreshCw } from "lucide-react"
import { OrdersList } from "@/components/orders-list"
import { DateRangePicker } from "@/components/date-range-picker"
import orderService, { type OrderQueryParams, type OrderStatus, type Order } from "@/lib/api/orderService"
import { useAuth } from "@/lib/context/auth-context"
import { toast } from "sonner"
import type { DateRange } from "react-day-picker"
import { format } from "date-fns"

export default function OrdersPage() {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [orders, setOrders] = useState<Order[]>([])
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNextPage: false,
    hasPrevPage: false,
  })

  // Convert tab value to API status
  const getStatusFromTab = (tab: string): OrderStatus | undefined => {
    switch (tab) {
      case "pending":
        return "PENDING"
      case "approved":
        return "APPROVED"
      case "history":
        return undefined // Special case handled in fetchOrders
      default:
        return undefined
    }
  }

  // Fetch orders based on active tab and search query
  const fetchOrders = useCallback(async () => {
    setIsLoading(true)
    try {
      const params: OrderQueryParams = {
        page: pagination.page,
        limit: pagination.limit,
        search: searchQuery || undefined,
      }

      // Handle special case for history tab
      if (activeTab === "history") {
        params.status = undefined // We'll filter on the client side
      } else {
        params.status = getStatusFromTab(activeTab)
      }

      // Add date range to params if selected
      if (dateRange?.from) {
        params.startDate = format(dateRange.from, "yyyy-MM-dd")
        if (dateRange.to) {
          params.endDate = format(dateRange.to, "yyyy-MM-dd")
        }
      }

      const response = await orderService.getAllOrders(params)
      console.log("response for orders api : ", response)


      // Check the structure of the response
      if (response && response.data) {
        // Set orders from the correct response structure
        setOrders(response.data.orders || [])
        // Set pagination from the correct response structure
        setPagination(
          response.data.pagination || {
            total: 0,
            page: 1,
            limit: 10,
            totalPages: 1,
            hasNextPage: false,
            hasPrevPage: false,
          },
        )
      } else {
        console.error("Unexpected response structure:", response)
        toast.error("Received unexpected data format from server")
      }
    } catch (error) {
      console.error("Error fetching orders:", error)
      toast.error("Failed to load orders. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }, [activeTab, searchQuery, pagination.page, pagination.limit, dateRange])

  // Initial data fetch
  useEffect(() => {
    fetchOrders()
  }, [fetchOrders])

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    // Reset to first page when changing tabs
    setPagination((prev) => ({ ...prev, page: 1 }))
  }

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
    // Reset to first page when searching
    setPagination((prev) => ({ ...prev, page: 1 }))
  }

  // Handle date range change
  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range)
    // Reset to first page when changing date range
    setPagination((prev) => ({ ...prev, page: 1 }))
  }

  // Handle pagination
  const handlePreviousPage = () => {
    if (pagination.page > 1) {
      setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
    }
  }

  const handleNextPage = () => {
    if (pagination.hasNextPage) {
      setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
    }
  }

  return (
    <DashboardShell>
      <DashboardHeader heading="Orders" text={`Manage customer orders`}>
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
          <div className="relative w-full sm:w-auto">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search orders..."
              className="w-full pl-8 sm:w-[200px] md:w-[300px]"
              value={searchQuery}
              onChange={handleSearch}
            />
          </div>
          <DateRangePicker
            onChange={handleDateRangeChange}
            placeholder="Filter by date"
          />
          <Button variant="outline" size="icon" onClick={fetchOrders}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </DashboardHeader>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList className="w-full sm:w-auto">
          <TabsTrigger value="all" className="flex-1 sm:flex-none">
            All Orders
          </TabsTrigger>
          <TabsTrigger value="pending" className="flex-1 sm:flex-none">
            Pending
          </TabsTrigger>
          <TabsTrigger value="approved" className="flex-1 sm:flex-none">
            Approved
          </TabsTrigger>
          <TabsTrigger value="history" className="flex-1 sm:flex-none">
            History
          </TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="space-y-4">
          <div className="text-sm text-muted-foreground">Showing All Orders</div>
          <OrdersList status="all" orders={orders} isLoading={isLoading} onRefresh={fetchOrders} />
        </TabsContent>
        <TabsContent value="pending" className="space-y-4">
          <div className="text-sm text-muted-foreground">Showing Pending Orders</div>
          <OrdersList status="PENDING" orders={orders} isLoading={isLoading} onRefresh={fetchOrders} />
        </TabsContent>
        <TabsContent value="approved" className="space-y-4">
          <div className="text-sm text-muted-foreground">Showing Approved Orders</div>
          <OrdersList status="APPROVED" orders={orders} isLoading={isLoading} onRefresh={fetchOrders} />
        </TabsContent>
        <TabsContent value="history" className="space-y-4">
          <div className="text-sm text-muted-foreground">Showing Order History</div>
          <OrdersList status="history" orders={orders} isLoading={isLoading} onRefresh={fetchOrders} />
        </TabsContent>
      </Tabs>

      {/* Pagination controls */}
      {!isLoading && orders.length > 0 && (
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 mt-4">
          <div className="text-sm text-muted-foreground">
            Showing {orders.length} of {pagination.total} orders
          </div>
          <div className="flex items-center space-x-2">
            <button
              className="px-3 py-1 text-sm border rounded-md disabled:opacity-50"
              onClick={handlePreviousPage}
              disabled={!pagination.hasPrevPage}
            >
              Previous
            </button>
            <div className="text-sm">
              Page {pagination.page} of {pagination.totalPages}
            </div>
            <button
              className="px-3 py-1 text-sm border rounded-md disabled:opacity-50"
              onClick={handleNextPage}
              disabled={!pagination.hasNextPage}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </DashboardShell>
  )
}
