"use client"

import { useState, useRef } from "react"
import { toast } from "sonner"
import { Download, Upload } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import stockService from "@/lib/api/stockService"

interface ImportStockDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function StockImportExportDialog({ open, onOpenChange, onSuccess }: ImportStockDialogProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [errors, setErrors] = useState<Array<{ row: number; message: string }>>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      // Check if it's a CSV file
      if (!selectedFile.name.endsWith('.csv')) {
        toast.error("Please select a CSV file")
        return
      }
      setFile(selectedFile)
      setErrors([])
    }
  }

  const handleExportStock = async () => {
    try {
      toast.info("Exporting stock data...");

      // Use the API client's endpoint directly
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';
      const response = await stockService.exportStockItems();

      // Create a download link for the blob
      const blob = new Blob([response], { type: 'text/csv' });
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = 'stock-export.csv';
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(downloadUrl);
      document.body.removeChild(a);

      toast.success("Stock data exported successfully");
    } catch (error) {
      console.error("Error exporting stock:", error);
      toast.error("Failed to export stock data. Please try again.");
    }
  }

  const handleImport = async () => {
    if (!file) {
      toast.error("Please select a CSV file to import")
      return
    }

    setIsUploading(true)
    setErrors([])

    console.log("Starting import with file:", file.name, file.type, file.size)

    try {
      toast.info("Importing stock data...");

      // Use the stock service to import the file
      const result = await stockService.importStockItems(file);

      console.log("Import result:", result);

      toast.success(`Successfully imported ${result.length} stock items`);
      onOpenChange(false);
      onSuccess();
      setFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error: any) {
      console.error("Error importing stock:", error)
      const errorMsg = error?.message || 'Import failed';
      toast.error(`Import failed: ${errorMsg}`);
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Stock Import/Export</DialogTitle>
          <DialogDescription>
            Upload a CSV file to import stock items or export current stock data to CSV.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="csv-file">CSV File</Label>
            <Input
              id="csv-file"
              type="file"
              accept=".csv"
              ref={fileInputRef}
              onChange={handleFileChange}
            />
            <p className="text-sm text-muted-foreground">
              {file ? `Selected file: ${file.name}` : "No file selected"}
            </p>
          </div>

          <Button
            type="button"
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={handleExportStock}
          >
            <Download className="mr-2 h-4 w-4" />
            Export Stock
          </Button>

          {errors.length > 0 && (
            <div className="mt-4 rounded-md bg-destructive/10 p-3">
              <h4 className="font-medium text-destructive">Import Errors:</h4>
              <ul className="mt-2 list-inside list-disc text-sm text-destructive">
                {errors.map((error, index) => (
                  <li key={index}>
                    Row {error.row}: {error.message}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isUploading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleImport}
            disabled={!file || isUploading}
          >
            {isUploading ? (
              "Importing..."
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Import
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
