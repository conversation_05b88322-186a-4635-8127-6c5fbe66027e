"use client"

import { useState, useRef } from "react"
import { toast } from "sonner"
import { Download, Upload } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import stockService from "@/lib/api/stockService"

interface ImportStockDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function StockImportExportDialog({ open, onOpenChange, onSuccess }: ImportStockDialogProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [errors, setErrors] = useState<Array<{ row: number; message: string }>>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      // Check if it's a CSV file
      if (!selectedFile.name.endsWith('.csv')) {
        toast.error("Please select a CSV file")
        return
      }
      setFile(selectedFile)
      setErrors([])
    }
  }

  const handleDownloadTemplate = async () => {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'
      const url = `${baseUrl}/stock/template`

      // Get the auth token
      const token = localStorage.getItem('auth_token')

      // Create headers with authorization token
      const headers: HeadersInit = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      toast.info("Downloading CSV template...")

      // Make the fetch request
      const response = await fetch(url, {
        method: 'GET',
        headers
      })

      if (!response.ok) {
        throw new Error('Failed to download template')
      }

      // Get the blob from the response
      const blob = await response.blob()

      // Create a download link
      const downloadUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = 'stock-import-template.csv'
      document.body.appendChild(a)
      a.click()

      // Clean up
      window.URL.revokeObjectURL(downloadUrl)
      document.body.removeChild(a)

      toast.success("CSV template downloaded successfully")
    } catch (error) {
      console.error("Error downloading template:", error)
      toast.error("Failed to download template. Please try again.")
    }
  }

  const handleExportStock = async () => {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'
      const url = `${baseUrl}/stock/export`

      // Get the auth token
      const token = localStorage.getItem('auth_token')

      // Create headers with authorization token
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      toast.info("Exporting stock data...")

      // Make the fetch request
      const response = await fetch(url, {
        method: 'GET',
        headers
      })

      if (!response.ok) {
        throw new Error('Failed to export stock data')
      }

      // Get the blob from the response
      const blob = await response.blob()

      // Create a download link
      const downloadUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = 'stock-export.csv'
      document.body.appendChild(a)
      a.click()

      // Clean up
      window.URL.revokeObjectURL(downloadUrl)
      document.body.removeChild(a)

      toast.success("Stock data exported successfully")
    } catch (error) {
      console.error("Error exporting stock:", error)
      toast.error("Failed to export stock data. Please try again.")
    }
  }

  const handleImport = async () => {
    if (!file) {
      toast.error("Please select a CSV file to import")
      return
    }

    setIsUploading(true)
    setErrors([])

    console.log("Starting import with file:", file.name, file.type, file.size)

    try {
      // Create a new FormData object directly here
      const formData = new FormData()
      formData.append('file', file)

      // Log the FormData contents (for debugging)
      console.log("FormData created with file:", file.name)

      // Use the API client directly to have more control
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'
      const url = `${baseUrl}/stock/import`

      // Get the auth token
      const token = localStorage.getItem('auth_token')

      // Create headers with only the authorization token
      const headers: HeadersInit = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      console.log("Sending request to:", url)

      // Make the fetch request directly
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData,
      })

      // Parse the response
      const data = await response.json()
      console.log("Response received:", data)

      if (response.ok && data.success) {
        toast.success(`Successfully imported ${data.imported} stock items`)
        onOpenChange(false)
        onSuccess()
        setFile(null)
        if (fileInputRef.current) {
          fileInputRef.current.value = ""
        }
      } else {
        const errorMsg = data.message || 'Import failed'
        toast.error(`Import failed: ${errorMsg}`)
        if (data.errors && data.errors.length > 0) {
          setErrors(data.errors)
        }
      }
    } catch (error) {
      console.error("Error importing stock:", error)
      toast.error("Failed to import stock. Please try again.")
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Stock Import/Export</DialogTitle>
          <DialogDescription>
            Download a CSV template, upload a CSV file to import stock items, or export current stock data to CSV.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleDownloadTemplate}
            >
              <Download className="mr-2 h-4 w-4" />
              Download Template
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleExportStock}
            >
              <Download className="mr-2 h-4 w-4" />
              Export Stock
            </Button>
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="csv-file">CSV File</Label>
            <Input
              id="csv-file"
              type="file"
              accept=".csv"
              ref={fileInputRef}
              onChange={handleFileChange}
            />
            <p className="text-sm text-muted-foreground">
              {file ? `Selected file: ${file.name}` : "No file selected"}
            </p>
          </div>

          {errors.length > 0 && (
            <div className="mt-4 rounded-md bg-destructive/10 p-3">
              <h4 className="font-medium text-destructive">Import Errors:</h4>
              <ul className="mt-2 list-inside list-disc text-sm text-destructive">
                {errors.map((error, index) => (
                  <li key={index}>
                    Row {error.row}: {error.message}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isUploading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleImport}
            disabled={!file || isUploading}
          >
            {isUploading ? (
              "Importing..."
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Import
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
