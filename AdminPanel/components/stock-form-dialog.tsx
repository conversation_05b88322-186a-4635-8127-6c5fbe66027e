"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import stockService, { type StockItem, type CreateStockItemDto, type UpdateStockItemDto } from "@/lib/api/stockService"

// Paper type options
const PAPER_TYPES = ["NS", "GY"]

// GSM options
const GSM_OPTIONS = [80, 100, 120, 140, 180, 200, 230, 250, 280, 300, 320, 330, 350]

// BF options
const BF_OPTIONS = [16, 18, 20, 22, 25, 28, 30, 35]

// Form schema
const stockFormSchema = z.object({
  type: z.string().min(1, "Type is required"), // Allow any string for type
  gsm: z.coerce.number().refine((val) => GSM_OPTIONS.includes(val), {
    message: `GSM must be one of: ${GSM_OPTIONS.join(', ')}`
  }),
  bf: z.coerce.number().refine((val) => BF_OPTIONS.includes(val), {
    message: `BF must be one of: ${BF_OPTIONS.join(', ')}`
  }),
  rollsAvailable: z.coerce.number().min(0, "Rolls available must be a non-negative number"),
  width: z.coerce.number().min(1, "Width must be a positive number"), // Allow any positive number for width
  immediatePrice: z.coerce.number().min(0.01, "Immediate price must be a positive number"),
  thirtyDayPrice: z.coerce.number().min(0.01, "30-day price must be a positive number"),
  sixtyDayPrice: z.coerce.number().min(0.01, "60-day price must be a positive number"),
})

type StockFormValues = z.infer<typeof stockFormSchema>

interface StockFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  stock?: StockItem
  onSuccess: () => void
}

export function StockFormDialog({ open, onOpenChange, stock, onSuccess }: StockFormDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = !!stock

  console.log("Is user Editing " , isEditing)

  // Initialize form
  const form = useForm<StockFormValues>({
    resolver: zodResolver(stockFormSchema),
    defaultValues: {
      type: stock?.type || "",
      gsm: stock?.gsm || 0,
      bf: stock?.bf || 0,
      rollsAvailable: stock?.rollsAvailable || 0,
      width: stock?.width || 0,
      immediatePrice: stock?.immediatePrice || 0,
      thirtyDayPrice: stock?.thirtyDayPrice || 0,
      sixtyDayPrice: stock?.sixtyDayPrice || 0,
    },
  })

  // Handle form submission
  const onSubmit = async (values: StockFormValues) => {
    setIsSubmitting(true)
    try {
      if (isEditing && stock) {
        // Update existing stock
        await stockService.updateStockItem(stock.id, values as UpdateStockItemDto)
        toast.success("Stock updated successfully")
      } else {
        // Create new stock
        await stockService.createStockItem(values as CreateStockItemDto)
        toast.success("Stock added successfully")
      }
      onOpenChange(false)
      onSuccess()
      form.reset()
    } catch (error) {
      console.error("Error submitting stock form:", error)
      toast.error(`Failed to ${isEditing ? "update" : "add"} stock. Please try again.`)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] max-h-[80vh] overflow-y-auto min-w-fit scrollber-hidden">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Stock" : "Add New Stock"}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update the details of the existing stock item."
              : "Add a new stock item to the inventory."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Paper Type</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., NS, GY, WS" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="gsm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>GSM</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    defaultValue={field.value ? field.value.toString() : undefined}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select GSM" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {GSM_OPTIONS.map((gsm) => (
                        <SelectItem key={gsm} value={gsm.toString()}>
                          {gsm}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="bf"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bursting Factor (BF)</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    defaultValue={field.value ? field.value.toString() : undefined}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select BF" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {BF_OPTIONS.map((bf) => (
                        <SelectItem key={bf} value={bf.toString()}>
                          {bf}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="rollsAvailable"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Available Rolls</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="e.g., 50" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="width"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Width (mm)</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="e.g., 1500" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Pricing Tiers</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="immediatePrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Immediate Payment (₹)</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" placeholder="e.g., 5000" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="thirtyDayPrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>30-Day Terms (₹)</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" placeholder="e.g., 5250" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="sixtyDayPrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>60-Day Terms (₹)</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" placeholder="e.g., 5500" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : isEditing ? "Update" : "Add"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
