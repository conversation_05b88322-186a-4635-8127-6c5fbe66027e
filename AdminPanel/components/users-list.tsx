"use client"

import { useState, useEffect } from "react"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Check, Phone, Mail, Building, X, Loader2 } from "lucide-react"
import userService from "@/lib/api/userService"
import { User } from "@/lib/types"
import { format } from "date-fns"
import { toast } from "sonner"
import { Skeleton } from "@/components/ui/skeleton"
import { Textarea } from "@/components/ui/textarea"
export interface UsersListProps {
  type?: string;
  onRefresh?: () => void;
}

export function UsersList({ type = "all", onRefresh }: UsersListProps) {
  const [users, setUsers] = useState<User[]>([])
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [rejectionReason, setRejectionReason] = useState("")

  // Fetch users from the API
  useEffect(() => {
    const fetchUsers = async () => {
      setIsLoading(true)
      try {
        const params: any = {
          page: 1,
          limit: 20
        }

        // Filter by approval status
        if (type === "new") {
          params.isApproved = false
        } else {
          params.isApproved = true
        }

        const response = await userService.getAllUsers(params)

        if (response.data && response.data.users) {
          setUsers(response.data.users)
        } else {
          console.error("Unexpected response structure:", response)
          toast.error("Failed to load users. Unexpected data format.")
        }
      } catch (error) {
        console.error("Error fetching users:", error)
        toast.error("Failed to load users. Please try again.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchUsers()
  }, [type])

  // Handle view details
  const handleViewDetails = async (user: User) => {
    try {
      // Get full user details
      const response = await userService.getUser(user.id)
      if (response.data && response.data.user) {
        setSelectedUser(response.data.user)
        setIsDialogOpen(true)
      }
    } catch (error) {
      console.error("Error fetching user details:", error)
      toast.error("Failed to load user details. Please try again.")
    }
  }

  // Handle approve user
  const handleApprove = async () => {
    if (!selectedUser) return

    setIsSubmitting(true)
    try {
      const response = await userService.approveUser(selectedUser.id)

      if (response.data && response.data.user) {
        toast.success("User approved successfully")
        setIsDialogOpen(false)

        // Refresh the users list
        if (onRefresh) {
          onRefresh()
        }
      }
    } catch (error) {
      console.error("Error approving user:", error)
      toast.error("Failed to approve user. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle reject user
  const handleReject = async () => {
    if (!selectedUser || !rejectionReason.trim()) {
      toast.error("Please provide a reason for rejection")
      return
    }

    setIsSubmitting(true)
    try {
      const response = await userService.rejectUser(selectedUser.id, { rejectionReason })

      if (response.data && response.data.user) {
        toast.success("User rejected successfully")
        setIsDialogOpen(false)

        // Refresh the users list
        if (onRefresh) {
          onRefresh()
        }
      }
    } catch (error) {
      console.error("Error rejecting user:", error)
      toast.error("Failed to reject user. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A"
    try {
      return format(new Date(dateString), 'dd MMM yyyy')
    } catch (error) {
      return dateString
    }
  }

  return (
    <>
      {isLoading ? (
        // Loading skeleton
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                    <Skeleton className="h-4 w-full" />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-muted/50 px-6 py-3">
                <Skeleton className="h-9 w-full" />
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : users.length === 0 ? (
        // No users found
        <div className="text-center py-10">
          <p className="text-muted-foreground">No users found</p>
        </div>
      ) : (
        // Users list
        <div className="space-y-4">
          {users.map((user) => (
            <Card key={user.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback>{user.contactPerson?.[0] || 'U'}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="font-semibold">{user.companyName}</div>
                      {!user.isApproved && <Badge variant="outline">New Request</Badge>}
                    </div>
                    <div className="text-sm text-muted-foreground">{user.email}</div>
                    <div className="text-sm">{user.contactPerson}</div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-muted/50 px-6 py-3">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => handleViewDetails(user)}
                >
                  View Details
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {selectedUser && (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarFallback>{selectedUser.contactPerson?.[0] || 'U'}</AvatarFallback>
                </Avatar>
                <span>{selectedUser.companyName}</span>
              </DialogTitle>
              <DialogDescription>
                {!selectedUser.isApproved
                  ? `Registration request on ${formatDate(selectedUser.createdAt)}`
                  : `Approved on ${formatDate(selectedUser.approvedAt)}`}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center gap-1">
                    <Phone className="h-3 w-3" /> Contact
                  </div>
                  <div className="font-medium">{selectedUser.contactNumber}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center gap-1">
                    <Mail className="h-3 w-3" /> Email
                  </div>
                  <div className="font-medium">{selectedUser.email}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center gap-1">
                    <Building className="h-3 w-3" /> GST Number
                  </div>
                  <div className="font-medium">{selectedUser.gstNumber}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center gap-1">
                    Contact Person
                  </div>
                  <div className="font-medium">{selectedUser.contactPerson}</div>
                </div>
              </div>

              {!selectedUser.isApproved && (
                <div className="space-y-2">
                  <h4 className="text-sm font-semibold">Rejection Reason (if rejecting)</h4>
                  <Textarea
                    placeholder="Enter reason for rejection..."
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                  />
                </div>
              )}
            </div>

            <DialogFooter>
              {!selectedUser.isApproved ? (
                <div className="w-full space-y-2">
                  <div className="font-medium">Do you want to approve this Account?</div>
                  <div className="flex gap-2">
                    <Button
                      className="w-full"
                      onClick={handleApprove}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Check className="mr-2 h-4 w-4" />
                      )}
                      Yes
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={handleReject}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <X className="mr-2 h-4 w-4" />
                      )}
                      No
                    </Button>
                  </div>
                </div>
              ) : null}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  )
}
