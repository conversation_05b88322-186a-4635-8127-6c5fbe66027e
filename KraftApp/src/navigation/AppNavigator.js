// This is an example of how your AppNavigator should be structured
import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { useAuth } from '../store/auth/AuthContext';

import AuthNavigator from './AuthNavigator';
import MainNavigator from './MainNavigator';
import ApprovalPendingScreen from '../screens/auth/ApprovalPendingScreen';
import LoadingScreen from '../screens/common/LoadingScreen';
import networkManager from '../utils/networkManager';
import tokenManager from '../utils/tokenManager';

const AppNavigator = () => {
  const { isAuthenticated, user, isLoading, checkAuthStatus, logout } = useAuth();

  // Initialize utilities and check auth status when the app starts
  useEffect(() => {
    // Initialize network monitoring
    networkManager.initialize();

    // Initialize token management
    tokenManager.initialize();

    // Handle token expiration by logging out
    const unsubscribeTokenExpired = tokenManager.onTokenExpired(() => {
      logout();
    });

    // Check auth status
    checkAuthStatus();

    // Cleanup on unmount
    return () => {
      networkManager.cleanup();
      tokenManager.cleanup();
      unsubscribeTokenExpired();
    };
  }, [logout]);

  // Show loading screen while checking auth
  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      {!isAuthenticated ? (
        // Not authenticated - show auth stack
        <AuthNavigator />
      ) : !user?.isApproved ? (
        // Authenticated but not approved - show approval pending
        <ApprovalPendingScreen />
      ) : (
        // Authenticated and approved - show main app
        <MainNavigator />
      )}
    </NavigationContainer>
  );
};

export default AppNavigator;