import { useEffect, useState } from 'react';
import messaging from '@react-native-firebase/messaging';
import auth from '@react-native-firebase/auth';
import { Platform } from 'react-native';
import { notificationApi } from '../api';

/**
 * Initialize Firebase services and register FCM token
 */
export const initializeFirebase = async (shouldRegisterToken = false) => {
  try {
    // Request permission for notifications (required for iOS)
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('Firebase Messaging permission granted');
      } else {
        console.log('Firebase Messaging permission denied');
        return null;
      }
    }

    // Get FCM token
    const token = await messaging().getToken();
    console.log('FCM Token:', token);

    // Register token with backend if requested and user is authenticated
    if (shouldRegisterToken && token) {
      try {
        await notificationApi.updateFCMToken(token);
        console.log('FCM token registered with backend successfully');
      } catch (error) {
        console.error('Failed to register FCM token with backend:', error);
        // Don't throw error here as Firebase initialization was successful
      }
    }

    return token;
  } catch (error) {
    console.error('Firebase initialization error:', error);
    return null;
  }
};

/**
 * Hook to handle Firebase messaging
 */
export const useFirebaseMessaging = () => {
  const [notification, setNotification] = useState(null);

  useEffect(() => {
    // Handle notifications when the app is in the foreground
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log('Foreground Message received:', remoteMessage);
      setNotification(remoteMessage);
    });

    // Handle notification when the app is in the background and opened
    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log('Background Message opened:', remoteMessage);
      setNotification(remoteMessage);
    });

    // Check if the app was opened from a notification
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log('App opened from quit state:', remoteMessage);
          setNotification(remoteMessage);
        }
      });

    return unsubscribe;
  }, []);

  return { notification };
};

/**
 * Phone authentication with Firebase
 */
export const usePhoneAuth = () => {
  const [confirm, setConfirm] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Send verification code
  const sendVerificationCode = async (phoneNumber) => {
    try {
      setLoading(true);
      setError(null);

      // Format phone number if needed
      const formattedPhoneNumber = phoneNumber.startsWith('+')
        ? phoneNumber
        : `+${phoneNumber}`;

      const authInstance = auth();
      const confirmation = await authInstance.signInWithPhoneNumber(formattedPhoneNumber);
      setConfirm(confirmation);
      setLoading(false);
      return true;
    } catch (err) {
      console.error('Error sending verification code:', err);
      setError(err.message);
      setLoading(false);
      return false;
    }
  };

  // Verify OTP code
  const verifyCode = async (code) => {
    try {
      setLoading(true);
      setError(null);

      if (!confirm) {
        setError('No verification was sent. Please request a new code.');
        setLoading(false);
        return false;
      }

      await confirm.confirm(code);
      setLoading(false);
      return true;
    } catch (err) {
      console.error('Error verifying code:', err);
      setError(err.message);
      setLoading(false);
      return false;
    }
  };

  return {
    sendVerificationCode,
    verifyCode,
    loading,
    error,
    confirm: !!confirm,
  };
};

// Firebase Phone Authentication functions

/**
 * Initialize phone authentication with reCAPTCHA verification
 * @param {string} phoneNumber - Phone number with country code
 * @param {string} recaptchaToken - reCAPTCHA verification token
 * @returns {Promise<object>} - Returns verification ID or error
 */
export const sendPhoneVerificationCode = async (phoneNumber) => {
  try {
    if (!phoneNumber) {
      return { success: false, error: 'Phone number is required' };
    }
    const formattedPhone = phoneNumber.startsWith('+')
      ? phoneNumber
      : `+91${phoneNumber}`;

    // Get the auth instance
    const authInstance = auth();
    const confirmation = await authInstance.signInWithPhoneNumber(formattedPhone);

    return {
      success: true,
      verificationId: confirmation.verificationId,
      expiresIn: 120,
    };
  } catch (error) {
    console.error('Phone verification error:', error.code, error.message);
    return { success: false, error: error.message || 'Failed to send verification code' };
  }
};
/**
 * Verify phone number with OTP code
 * @param {string} verificationId - Verification ID received from sendPhoneVerificationCode
 * @param {string} code - 6-digit OTP code
 * @returns {Promise<object>} - Returns success status or error
 */
export const verifyPhoneNumber = async (verificationId, code) => {
  try {
    // Validate inputs
    if (!verificationId) {
      return {
        success: false,
        error: 'Verification ID is required',
      };
    }

    if (!code) {
      return {
        success: false,
        error: 'OTP code is required',
      };
    }

    if (code.length !== 6) {
      return {
        success: false,
        error: 'OTP code must be 6 digits',
      };
    }

    // Get the auth instance
    const authInstance = auth();

    // Create a credential with the verification ID and code
    const credential = auth.PhoneAuthProvider.credential( verificationId, code);

    // Sign in with the credential
    const userCredential = await authInstance.signInWithCredential(credential);

    // Get the user token
    const firebaseToken = await userCredential.user.getIdToken();

    return {
      success: true,
      firebaseToken,
      firebaseUid: userCredential.user.uid,
      user: userCredential.user,
    };
  } catch (error) {
    console.error('Phone verification error:', error);
    return {
      success: false,
      error: error.message || 'Failed to verify phone number',
    };
  }
};

export const registerWithFirebase = async (email, password) => {
  const authInstance = auth();
  try {
    console.log('Attempting Firebase registration for email:', email);
    const userCredential = await authInstance.createUserWithEmailAndPassword(email, password);
    console.log('Firebase registration successful, UID:', userCredential.user.uid);
    return {
      success: true,
      uid: userCredential.user.uid,
      isNewUser: true,
    };
  } catch (error) {
    console.error('Firebase registration error:', error.code, error.message);

    // Handle specific Firebase error codes
    switch (error.code) {
      case 'auth/email-already-in-use':
        console.log('Email already exists, attempting to sign in...');
        try {
          // User already exists, try to sign in to get their UID
          const signInCredential = await authInstance.signInWithEmailAndPassword(email, password);
          console.log('Firebase sign-in successful for existing user, UID:', signInCredential.user.uid);
          return {
            success: true,
            uid: signInCredential.user.uid,
            isNewUser: false,
            message: 'User already exists, signed in successfully',
          };
        } catch (signInError) {
          console.error('Firebase sign-in failed for existing user:', signInError.code, signInError.message);

          if (signInError.code === 'auth/wrong-password') {
            throw new Error('An account with this email already exists but with a different password. Please use the correct password or reset it.');
          } else if (signInError.code === 'auth/user-disabled') {
            throw new Error('This account has been disabled. Please contact support.');
          } else if (signInError.code === 'auth/too-many-requests') {
            throw new Error('Too many failed attempts. Please try again later.');
          } else {
            throw new Error('An account with this email already exists. Please try logging in instead.');
          }
        }

      case 'auth/weak-password':
        throw new Error('Password is too weak. Please choose a stronger password.');

      case 'auth/invalid-email':
        throw new Error('Invalid email address format.');

      case 'auth/operation-not-allowed':
        throw new Error('Email/password accounts are not enabled. Please contact support.');

      case 'auth/network-request-failed':
        throw new Error('Network error. Please check your internet connection and try again.');

      case 'auth/too-many-requests':
        throw new Error('Too many requests. Please try again later.');

      default:
        console.error('Unhandled Firebase error:', error.code, error.message);
        throw new Error(`Registration failed: ${error.message || 'Unknown error occurred'}`);
    }
  }
};

export default {
  initializeFirebase,
  useFirebaseMessaging,
  usePhoneAuth,
};
