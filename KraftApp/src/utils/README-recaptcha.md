# Firebase reCAPTCHA Integration

This documentation explains how the Firebase reCAPTCHA verification has been integrated into the mobile app for phone OTP authentication.

## Overview

To protect the application from abuse and automated attacks, Firebase requires reCAPTCHA verification when using phone number authentication. This implementation includes:

1. A custom hook `useFirebaseRecaptcha()` for managing reCAPTCHA tokens
2. A component `FirebaseRecaptcha` that loads the verification challenge
3. Integration with the backend API for sending and verifying OTPs with reCAPTCHA tokens

## Implementation Details

### Development Mode

In development mode:
- A dummy reCAPTCHA token is generated to simplify testing
- The FirebaseRecaptcha component doesn't actually render a visible reCAPTCHA challenge
- The backend accepts the dummy token for development environments

### Production Mode

In production mode:
- The application loads the real Google reCAPTCHA using a WebView
- The user may see a reCAPTCHA challenge when sending phone verification codes
- The reCAPTCHA tokens are sent to the backend and verified with Firebase

## Configuration

Configuration is managed in the `src/config.js` file:

```javascript
export const FIREBASE_CONFIG = {
  development: {
    recaptchaEnabled: false,
    webApiKey: 'your-firebase-web-api-key'
  },
  production: {
    recaptchaEnabled: true,
    webApiKey: 'your-firebase-web-api-key'
  }
};
```

## Usage

To use reCAPTCHA in a component:

```javascript
import { useFirebaseRecaptcha, FirebaseRecaptcha } from '../utils/recaptcha';
import { sendPhoneVerificationCode } from '../utils/firebase-auth';

function MyComponent() {
  // Use the hook to get access to the reCAPTCHA functionality
  const { recaptchaRef, getRecaptchaToken } = useFirebaseRecaptcha();
  
  // Function to send OTP with reCAPTCHA verification
  const sendOTP = async () => {
    // Get a reCAPTCHA token
    const token = await getRecaptchaToken();
    
    // Send the verification code with the token
    const result = await sendPhoneVerificationCode(phoneNumber, token);
    
    if (result.success) {
      // Handle success
    } else {
      // Handle error
    }
  };
  
  return (
    <View>
      {/* Your UI components */}
      
      {/* Add the invisible reCAPTCHA component */}
      <FirebaseRecaptcha
        ref={recaptchaRef}
        onTokenReceived={(token) => {
          console.log('reCAPTCHA token received:', token);
        }}
      />
    </View>
  );
}
```

## Error Handling

The implementation includes robust error handling:

- Network connectivity checks before API requests
- Retry logic for network failures
- User-friendly error messages for common issues
- Timeout handling for API requests

## Production Considerations

Before deploying to production:

1. Replace the Firebase Web API key with your actual production key
2. Enable reCAPTCHA in the config file by setting `recaptchaEnabled: true`
3. Test thoroughly in a staging environment
4. Ensure your backend properly validates reCAPTCHA tokens with Firebase
5. Consider implementing rate limiting on the backend for additional security

## Dependencies

- `react-native-webview` - Required for loading reCAPTCHA in a WebView
- Proper Firebase configuration in your backend services

## Troubleshooting

Common issues and solutions:

1. **reCAPTCHA not showing**: Check that you're using the correct Firebase Web API key and that the component is properly mounted.

2. **Token verification failing**: Ensure your backend is correctly configured to validate tokens with Firebase.

3. **Network errors**: Implement proper error handling and retry logic as demonstrated in the code.

4. **Expired tokens**: reCAPTCHA tokens expire after a short period. Implement logic to refresh tokens when needed.

## Further Enhancements

Potential improvements for the future:

1. Add a visible reCAPTCHA checkbox option for higher security needs
2. Implement caching of successful verifications to reduce friction
3. Add analytics to track verification success rates and error patterns
