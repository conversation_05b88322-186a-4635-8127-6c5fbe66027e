/**
 * Simple logger utility for consistent logging throughout the application
 */

// Configuration
const config = {
  // Enable logging in production (set to false for production builds)
  enableInProduction: false,
  
  // Minimum log level to show
  // 0 = debug, 1 = info, 2 = warn, 3 = error
  minLevel: __DEV__ ? 0 : 2,
  
  // Enable timestamps in logs
  showTimestamps: true,
  
  // Enable network logging for specific API modules
  enabledNetworkLogs: {
    auth: true,    // Authentication API calls
    stock: true,   // Stock management API calls
    cart: true,    // Cart management API calls
    order: true,   // Order management API calls 
    enquiry: true, // Enquiry management API calls
  },
};

// Log levels
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
};

// Log level emoji prefixes
const LOG_PREFIXES = {
  [LOG_LEVELS.DEBUG]: '🔍',
  [LOG_LEVELS.INFO]: 'ℹ️',
  [LOG_LEVELS.WARN]: '⚠️',
  [LOG_LEVELS.ERROR]: '❌',
};

/**
 * Check if logging is enabled based on environment and config
 * @returns {boolean}
 */
const isLoggingEnabled = () => {
  if (__DEV__) return true;
  return config.enableInProduction;
};

/**
 * Format timestamp for logs
 * @returns {string}
 */
const getTimestamp = () => {
  if (!config.showTimestamps) return '';
  return `[${new Date().toISOString()}]`;
};

/**
 * Log message with specific level
 * @param {number} level - Log level
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
const logWithLevel = (level, message, data) => {
  if (!isLoggingEnabled() || level < config.minLevel) return;
  
  const prefix = LOG_PREFIXES[level] || '';
  const timestamp = getTimestamp();
  
  if (!data) {
    console.log(`${prefix} ${timestamp} ${message}`);
  } else {
    console.log(`${prefix} ${timestamp} ${message}`, data);
    
    // If it's an error, also log the stack trace
    if (data instanceof Error && __DEV__) {
      console.log(data.stack);
    }
  }
};

/**
 * Log debug message
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
const debug = (message, data) => {
  logWithLevel(LOG_LEVELS.DEBUG, message, data);
};

/**
 * Log info message
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
const info = (message, data) => {
  logWithLevel(LOG_LEVELS.INFO, message, data);
};

/**
 * Log warning message
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
const warn = (message, data) => {
  logWithLevel(LOG_LEVELS.WARN, message, data);
};

/**
 * Log error message
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
const error = (message, data) => {
  logWithLevel(LOG_LEVELS.ERROR, message, data);
};

/**
 * Track API function call with timing
 * @param {string} module - API module name (auth, stock, etc.)
 * @param {string} functionName - Function name being called
 * @param {function} apiCall - Async function making the API call
 * @returns {Promise} - Result of the API call
 */
const trackApiCall = async (module, functionName, apiCall) => {
  // Skip if network logging is disabled for this module
  if (!isLoggingEnabled() || !config.enabledNetworkLogs[module]) {
    return apiCall();
  }
  
  const requestId = Math.random().toString(36).substring(2, 8);
  
  try {
    debug(`${module}.${functionName} called [${requestId}]`);
    const start = Date.now();
    
    const result = await apiCall();
    
    const duration = Date.now() - start;
    info(`${module}.${functionName} succeeded [${requestId}] (${duration}ms)`);
    
    return result;
  } catch (err) {
    error(`${module}.${functionName} failed [${requestId}]`, err);
    throw err;
  }
};

export default {
  debug,
  info,
  warn,
  error,
  trackApiCall,
  
  // Export configuration for runtime updates
  config,
};
