'use client';

import { useState, useEffect, useCallback } from 'react'
import { useFocusEffect } from '@react-navigation/native';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';
import Button from '../components/common/Button';
import { enquiryApi } from '../api';
import errorHandler from '../utils/errorHandler';
import alertManager from '../utils/alertManager';

const EnquiriesScreen = () => {
  const [type, setType] = useState('');
  const [gsm, setGsm] = useState('');
  const [bf, setBf] = useState('');
  const [width, setWidth] = useState('');
  const [quantity, setQuantity] = useState('1');
  const [message, setMessage] = useState('');
  const [contactPreference, setContactPreference] = useState('EMAIL');
  const [typeError, setTypeError] = useState('');
  const [gsmError, setGsmError] = useState('');
  const [bfError, setBfError] = useState('');
  const [widthError, setWidthError] = useState('');
  const [quantityError, setQuantityError] = useState('');
  const [messageError, setMessageError] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [enquiries, setEnquiries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(true);

  // Paper type options
  const paperTypeOptions = [
    { label: 'Kraft Paper', value: 'KRAFT' },
    { label: 'Test Liner', value: 'TEST_LINER' },
    { label: 'Medium', value: 'MEDIUM' },
    { label: 'Other', value: 'OTHER' },
  ];

  // Contact preference options
  const contactPreferenceOptions = [
    { label: 'Email', value: 'EMAIL' },
    { label: 'Phone', value: 'PHONE' },
  ];

  // Refresh enquiries every time the screen is focused to ensure fresh data
  useFocusEffect(
    useCallback(() => {
      console.log('Enquiries screen focused, refreshing enquiries...');
      fetchEnquiries();
      return () => {
        // Cleanup function when screen is unfocused
        console.log('Enquiries screen unfocused');
      };
    }, [])
  );

  // Fetch enquiries from API
  const fetchEnquiries = async () => {
    setLoading(true)
    try {
      const response = await enquiryApi.getEnquiries()
      console.log('Enquiries response:', response)

      // Handle different response formats
      if (response?.enquiries && Array.isArray(response.enquiries)) {
        setEnquiries(response.enquiries)
      } else if (response?.data?.enquiries && Array.isArray(response.data.enquiries)) {
        setEnquiries(response.data.enquiries)
      } else if (Array.isArray(response)) {
        setEnquiries(response)
      } else {
        // If we can't find enquiries in the expected format, set empty array
        console.warn('Unexpected enquiries response format:', response)
        setEnquiries([])
      }
    } catch (error) {
      console.error('Error fetching enquiries:', error)
      errorHandler.handleApiError(error, {
        context: 'fetch_enquiries',
        fallbackMessage: 'Failed to load enquiries. Please try again.',
      })
      setEnquiries([]) // Set empty array on error
    } finally {
      setLoading(false)
    }
  }

  // Validate form fields
  const validateForm = () => {
    let isValid = true

    // Reset error messages
    setTypeError('')
    setGsmError('')
    setBfError('')
    setWidthError('')
    setQuantityError('')
    setMessageError('')

    // Validate type
    if (!type.trim()) {
      setTypeError('Paper type is required')
      isValid = false
    }

    // Validate GSM
    if (!gsm.trim()) {
      setGsmError('GSM is required')
      isValid = false
    } else if (isNaN(gsm) || parseFloat(gsm) <= 0) {
      setGsmError('GSM must be a valid positive number')
      isValid = false
    }

    // Validate BF
    if (!bf.trim()) {
      setBfError('BF is required')
      isValid = false
    } else if (isNaN(bf) || parseFloat(bf) <= 0) {
      setBfError('BF must be a valid positive number')
      isValid = false
    }

    // Validate width
    if (!width.trim()) {
      setWidthError('Width is required')
      isValid = false
    } else if (isNaN(width) || parseFloat(width) <= 0) {
      setWidthError('Width must be a valid positive number')
      isValid = false
    }

    // Validate quantity
    if (!quantity.trim()) {
      setQuantityError('Quantity is required')
      isValid = false
    } else if (isNaN(quantity) || parseInt(quantity, 10) <= 0) {
      setQuantityError('Quantity must be a valid positive integer');
      isValid = false;
    }

    // Validate message (optional)
    if (message.trim() && message.trim().length > 1000) {
      setMessageError('Message cannot exceed 1000 characters')
      isValid = false
    }

    return isValid
  }

  // Handle submit enquiry
  const handleSubmitEnquiry = async () => {
    // Validate form
    if (!validateForm()) {
      return
    }

    setSubmitting(true)
    try {
      await enquiryApi.submitEnquiry({
        type: type.trim(),
        gsm: gsm.trim(),
        bf: bf.trim(),
        width: width.trim(),
        quantity: parseInt(quantity.trim(), 10),
        message: message.trim() || null,
        contactPreference,
      })

      // Reset form
      setType('')
      setGsm('')
      setBf('')
      setWidth('')
      setQuantity('1')
      setMessage('')
      setContactPreference('EMAIL')
      setTypeError('')
      setGsmError('')
      setBfError('')
      setWidthError('')
      setQuantityError('')
      setMessageError('')

      // Show success message using custom alert manager
      alertManager.showSuccess(
        'Enquiry Submitted',
        'Your enquiry has been submitted successfully. We\'ll get back to you soon.',
        [
          {
            text: 'OK',
            onPress: () => {
              setShowForm(false)
              fetchEnquiries()
            },
          },
        ]
      )
    } catch (error) {
      // Check for validation errors from the API
      if (error.response?.status === 400 && error.data?.errors) {
        const validationErrors = error.data.errors

        // Set field-specific errors
        validationErrors.forEach(err => {
          if (err.field === 'type') {
            setTypeError(err.message)
          } else if (err.field === 'gsm') {
            setGsmError(err.message)
          } else if (err.field === 'bf') {
            setBfError(err.message)
          } else if (err.field === 'width') {
            setWidthError(err.message)
          } else if (err.field === 'quantity') {
            setQuantityError(err.message)
          } else if (err.field === 'message') {
            setMessageError(err.message)
          }
        })
      } else {
        errorHandler.handleApiError(error, {
          context: 'submit_enquiry',
          fallbackMessage: 'Failed to submit enquiry. Please try again.',
        })
      }
    } finally {
      setSubmitting(false)
    }
  }

  // Format date for display
  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString()
    } catch (error) {
      return 'Unknown date'
    }
  }

  // Render enquiry item
  const renderEnquiryItem = ({ item }) => (
    <View style={styles.enquiryItem}>
      <View style={styles.enquiryHeader}>
        <Text style={styles.enquiryTitle}>
          {item.type || 'Unknown Type'} - {item.gsm}GSM
        </Text>
        <Text style={styles.enquiryDate}>{formatDate(item.createdAt)}</Text>
      </View>

      <View style={styles.enquirySpecs}>
        <Text style={styles.enquirySpec}>GSM: {item.gsm}</Text>
        <Text style={styles.enquirySpec}>BF: {item.bf}</Text>
        <Text style={styles.enquirySpec}>Width: {item.width}</Text>
        <Text style={styles.enquirySpec}>Quantity: {item.quantity}</Text>
      </View>

      {item.message && (
        <Text style={styles.enquiryMessage}>{item.message}</Text>
      )}

      {item.enquiryNumber && (
        <Text style={styles.enquiryNumber}>Ref: {item.enquiryNumber}</Text>
      )}

      <View style={styles.enquiryFooter}>
        <Text
          style={[
            styles.enquiryStatus,
            item.status === 'ANSWERED' || item.status === 'RESOLVED'
              ? styles.answeredStatus
              : styles.pendingStatus
          ]}
        >
          {item.status || 'PENDING'}
        </Text>

        {item.response && (
          <TouchableOpacity
            style={styles.viewResponseButton}
            onPress={() => alertManager.showInfo('Response', item.response)}
          >
            <Text style={styles.viewResponseText}>View Response</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  )

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Paper Enquiries</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.tabContainer}>
          <TouchableOpacity style={[styles.tab, showForm && styles.activeTab]} onPress={() => setShowForm(true)}>
            <Text style={[styles.tabText, showForm && styles.activeTabText]}>New Enquiry</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.tab, !showForm && styles.activeTab]} onPress={() => setShowForm(false)}>
            <Text style={[styles.tabText, !showForm && styles.activeTabText]}>My Enquiries</Text>
          </TouchableOpacity>
        </View>

        {showForm ? (
          <ScrollView style={styles.formContainer}>
            <Text style={styles.label}>Paper Type</Text>
           <TextInput
              style={[styles.input, typeError ? styles.inputError : null]}
              value={type}
              onChangeText={(text) => {
                setType(text)
                if (typeError) setTypeError('')
              }}
              placeholder="Enter paper type"
            />
            {typeError ? <Text style={styles.errorText}>{typeError}</Text> : null}

            {/* GSM and BF in a single row */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Text style={styles.label}>GSM</Text>
                <TextInput
                  style={[styles.input, gsmError ? styles.inputError : null]}
                  value={gsm}
                  onChangeText={(text) => {
                    setGsm(text)
                    if (gsmError) setGsmError('')
                  }}
                  placeholder="Enter GSM (e.g., 120)"
                  keyboardType="numeric"
                />
                {gsmError ? <Text style={styles.errorText}>{gsmError}</Text> : null}
              </View>

              <View style={styles.halfWidth}>
                <Text style={styles.label}>BF</Text>
                <TextInput
                  style={[styles.input, bfError ? styles.inputError : null]}
                  value={bf}
                  onChangeText={(text) => {
                    setBf(text)
                    if (bfError) setBfError('')
                  }}
                  placeholder="Enter BF value"
                  keyboardType="numeric"
                />
                {bfError ? <Text style={styles.errorText}>{bfError}</Text> : null}
              </View>
            </View>

            {/* Width and Quantity in a single row */}
            <View style={styles.rowContainer}>
              <View style={styles.halfWidth}>
                <Text style={styles.label}>Width</Text>
                <TextInput
                  style={[styles.input, widthError ? styles.inputError : null]}
                  value={width}
                  onChangeText={(text) => {
                    setWidth(text)
                    if (widthError) setWidthError('')
                  }}
                  placeholder="Enter width (e.g., 1200mm)"
                />
                {widthError ? <Text style={styles.errorText}>{widthError}</Text> : null}
              </View>

              <View style={styles.halfWidth}>
                <Text style={styles.label}>Quantity</Text>
                <View style={styles.quantityContainer}>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => setQuantity(Math.max(1, parseInt(quantity || '1') - 1).toString())}
                  >
                    <Text style={styles.quantityButtonText}>-</Text>
                  </TouchableOpacity>
                  <TextInput
                    style={[styles.quantityInput, quantityError ? styles.inputError : null]}
                    value={quantity}
                    onChangeText={(text) => {
                      setQuantity(text)
                      if (quantityError) setQuantityError('')
                    }}
                    placeholder="1"
                    keyboardType="numeric"
                    textAlign="center"
                  />
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => setQuantity((parseInt(quantity || '1') + 1).toString())}
                  >
                    <Text style={styles.quantityButtonText}>+</Text>
                  </TouchableOpacity>
                </View>
                {quantityError ? <Text style={styles.errorText}>{quantityError}</Text> : null}
              </View>
            </View>

            <Text style={styles.label}>Additional Message (Optional)</Text>
            <TextInput
              style={[styles.messageInput, messageError ? styles.inputError : null]}
              value={message}
              onChangeText={(text) => {
                setMessage(text)
                if (messageError) setMessageError('')
              }}
              placeholder="Any additional requirements or notes..."
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
            {messageError ? <Text style={styles.errorText}>{messageError}</Text> : null}

           
            <Button
              title="Submit Enquiry"
              onPress={handleSubmitEnquiry}
              loading={submitting}
              style={styles.submitButton}
            />
          </ScrollView>
        ) : (
          <>
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={styles.loadingText}>Loading enquiries...</Text>
              </View>
            ) : enquiries.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>You haven\'t submitted any enquiries yet</Text>
                <Button
                  title="Submit New Enquiry"
                  variant="outline"
                  onPress={() => setShowForm(true)}
                  style={styles.newEnquiryButton}
                />
              </View>
            ) : (
              <FlatList
                data={enquiries}
                renderItem={renderEnquiryItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
              />
            )}
          </>
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.xlarge,
    paddingHorizontal: spacing.large,
    alignItems: 'center',
  },
  headerTitle: {
    ...textStyles.heading2,
    color: colors.white,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    marginTop: -20,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: spacing.medium,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 8,
    marginBottom: spacing.medium,
    overflow: 'hidden',
    elevation: 2,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.medium,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  tabText: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '500',
  },
  activeTabText: {
    color: colors.white,
  },
  formContainer: {
    flex: 1,
  },
  label: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '500',
    marginBottom: spacing.small,
    marginTop: spacing.medium,
  },
  input: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.medium,
    marginBottom: spacing.small,
    ...textStyles.body1,
  },
  messageInput: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.medium,
    marginBottom: spacing.small,
    ...textStyles.body1,
    minHeight: 100,
  },
  inputError: {
    borderColor: colors.error || 'red',
  },
  errorText: {
    ...textStyles.caption,
    color: colors.error || 'red',
    marginBottom: spacing.medium,
    marginTop: -spacing.small,
  },
  noteText: {
    ...textStyles.body2,
    color: colors.textLight,
    fontStyle: 'italic',
    marginBottom: spacing.medium,
    marginTop: spacing.medium,
  },
  submitButton: {
    marginBottom: spacing.large,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...textStyles.body1,
    color: colors.textLight,
    marginTop: spacing.medium,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    ...textStyles.body1,
    color: colors.textLight,
    marginBottom: spacing.medium,
  },
  newEnquiryButton: {
    width: 200,
  },
  listContainer: {
    paddingBottom: spacing.large,
  },
  enquiryItem: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: spacing.medium,
    marginBottom: spacing.medium,
    elevation: 2,
  },
  enquiryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.small,
  },
  enquiryTitle: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '600',
    flex: 1,
  },
  enquiryDate: {
    ...textStyles.caption,
    color: colors.textLight,
  },
  enquirySpecs: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.small,
  },
  enquirySpec: {
    ...textStyles.caption,
    color: colors.textDark,
    backgroundColor: colors.lightGray || '#f5f5f5',
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny,
    borderRadius: 4,
    marginRight: spacing.small,
    marginBottom: spacing.tiny,
  },
  enquiryMessage: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.medium,
    fontStyle: 'italic',
  },
  enquiryNumber: {
    ...textStyles.caption,
    color: colors.textLight,
    marginBottom: spacing.small,
  },
  enquiryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  enquiryStatus: {
    ...textStyles.caption,
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny,
    borderRadius: 4,
    overflow: 'hidden',
  },
  pendingStatus: {
    backgroundColor: colors.warning,
    color: colors.white,
  },
  answeredStatus: {
    backgroundColor: colors.success,
    color: colors.white,
  },
  viewResponseButton: {
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny,
  },
  viewResponseText: {
    ...textStyles.body2,
    color: colors.primary,
    fontWeight: '500',
  },
  // New styles for improved layout
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.medium,
  },
  halfWidth: {
    flex: 1,
    marginHorizontal: spacing.tiny,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    marginBottom: spacing.small,
  },
  quantityButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.lightGray || '#f5f5f5',
  },
  quantityButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.textDark,
  },
  quantityInput: {
    flex: 1,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.medium,
    ...textStyles.body1,
    textAlign: 'center',
  },
})

export default EnquiriesScreen
