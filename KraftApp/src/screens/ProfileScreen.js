"use client"

import { useState, useEffect, useCallback } from "react"
import { useFocusEffect } from '@react-navigation/native'
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from "react-native"
import { colors } from "../theme/colors"
import { spacing } from "../theme/spacing"
import { textStyles } from "../theme/typography"
import StatusBarManager from "../components/common/StatusBarManager"
import Button from "../components/common/Button"
import { useAuth } from "../store/auth/AuthContext"
import { authApi } from "../api"
import errorHandler from "../utils/errorHandler"
import { useNavigation } from "@react-navigation/native"

const ProfileScreen = () => {
  const { user: authUser, logout } = useAuth()
  const navigation = useNavigation()
  const [loading, setLoading] = useState(false)
  const [profileData, setProfileData] = useState(null)

  // Extract user data properly - either from auth context or from profile data
  const user = profileData?.user || authUser?.user || authUser || null;

  console.log("user in profile page:", user);

  // Refresh profile data every time the screen is focused to ensure fresh data
  useFocusEffect(
    useCallback(() => {
      console.log('Profile screen focused, refreshing profile...');
      // Only fetch if we don't have profile data or if it's been more than 5 minutes
      const shouldFetch = !profileData ||
        (profileData.lastFetched && Date.now() - profileData.lastFetched > 5 * 60 * 1000);

      if (shouldFetch) {
        fetchProfile();
      }
      return () => {
        // Cleanup function when screen is unfocused
        console.log('Profile screen unfocused');
      };
    }, [profileData])
  );

  // Fetch profile from API
  const fetchProfile = async () => {
    setLoading(true)
    try {
      console.log('Fetching profile data...');
      const response = await authApi.getCurrentUser();
      console.log('Profile data received:', response);

      if (response) {
        // Add timestamp to track when data was fetched
        const profileDataWithTimestamp = {
          ...response,
          lastFetched: Date.now()
        };
        setProfileData(profileDataWithTimestamp);
      } else {
        console.warn('No profile data returned from API');
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      errorHandler.handleApiError(error, {
        context: "fetch_profile",
        fallbackMessage: "Failed to load profile data. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  }

  // Handle logout
  const handleLogout = async () => {
    Alert.alert("Confirm Logout", "Are you sure you want to logout?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Logout",
        style: "destructive",
        onPress: async () => {
          setLoading(true)
          try {
            await logout()
            // Navigation will be handled by the auth context
          } catch (error) {
            errorHandler.handleApiError(error, {
              context: "logout",
              fallbackMessage: "Failed to logout. Please try again.",
            })
          } finally {
            setLoading(false)
          }
        },
      },
    ])
  }

  // Get user's initials for avatar
  const getInitials = () => {
    if (!user || !user.contactPerson) return "?"

    const names = user.contactPerson.split(" ")
    if (names.length > 1) {
      return `${names[0][0]}${names[1][0]}`.toUpperCase()
    }

    return names[0][0].toUpperCase()
  }

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Profile</Text>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={styles.avatarContainer}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>{getInitials()}</Text>
          </View>
          <Text style={styles.userName}>{user?.contactPerson || "User"}</Text>
          <Text style={styles.userCompany}>{user?.companyName || "Company"}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Company Information</Text>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>GST Number</Text>
            <Text style={styles.infoValue}>{user?.gstNumber || "Not provided"}</Text>
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Contact Email</Text>
            <Text style={styles.infoValue}>{user?.email || "Not provided"}</Text>
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Contact Phone</Text>
            <Text style={styles.infoValue}>{user?.contactNumber || "Not provided"}</Text>
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Payment Terms</Text>
            <Text style={styles.infoValue}>
              {user?.paymentTerms ? user.paymentTerms.replace("_", " ") : "Not provided"}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account Settings</Text>

          <TouchableOpacity
            style={styles.settingsItem}
            onPress={() => navigation.navigate('ChangePassword')}
          >
            <Text style={styles.settingsItemText}>Change Password</Text>
          </TouchableOpacity>

          {/* <TouchableOpacity style={styles.settingsItem} onPress={() => navigation.navigate('EditCompanyInformation')}>
            <Text style={styles.settingsItemText}>Edit Company Information</Text>
          </TouchableOpacity> */}
{/* 
          <TouchableOpacity
            style={styles.settingsItem}
            onPress={() => navigation.navigate('ShippingAddresses')}
          >
            <Text style={styles.settingsItemText}>Shipping Addresses</Text>
          </TouchableOpacity> */}

          <TouchableOpacity
            style={styles.settingsItem}
            onPress={() => navigation.navigate('Notifications')}
          >
            <Text style={styles.settingsItemText}>Notifications</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingsItem}
            onPress={() => navigation.navigate('PaymentTermUpgrade')}
          >
            <Text style={styles.settingsItemText}>Payment Term Upgrade</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>

          <TouchableOpacity
            style={styles.settingsItem}
            onPress={() => navigation.navigate('Support')}
          >
            <Text style={styles.settingsItemText}>Contact Support</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingsItem}
            onPress={() => navigation.navigate('FAQ')}
          >
            <Text style={styles.settingsItemText}>Frequently Asked Questions</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingsItem}
            onPress={() => navigation.navigate('TermsAndConditions')}
          >
            <Text style={styles.settingsItemText}>Terms & Conditions</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingsItem}
            onPress={() => navigation.navigate('PrivacyPolicy')}
          >
            <Text style={styles.settingsItemText}>Privacy Policy</Text>
          </TouchableOpacity>
        </View>

        <Button title="Logout" variant="outline" onPress={handleLogout} loading={loading} style={styles.logoutButton} />
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.xlarge,
    paddingHorizontal: spacing.large,
    alignItems: "center",
  },
  headerTitle: {
    ...textStyles.heading2,
    color: colors.white,
    fontWeight: "bold",
  },
  content: {
    flex: 1,
    marginTop: -20,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  contentContainer: {
    padding: spacing.medium,
    paddingBottom: spacing.xlarge,
  },
  avatarContainer: {
    alignItems: "center",
    marginVertical: spacing.large,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.medium,
  },
  avatarText: {
    ...textStyles.heading1,
    color: colors.white,
  },
  userName: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.tiny,
  },
  userCompany: {
    ...textStyles.body1,
    color: colors.textLight,
  },
  section: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    marginBottom: spacing.large,
    elevation: 2,
  },
  sectionTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.medium,
  },
  infoItem: {
    marginBottom: spacing.medium,
  },
  infoLabel: {
    ...textStyles.body2,
    color: colors.textLight,
    marginBottom: spacing.tiny,
  },
  infoValue: {
    ...textStyles.body1,
    color: colors.textDark,
  },
  settingsItem: {
    paddingVertical: spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingsItemText: {
    ...textStyles.body1,
    color: colors.textDark,
  },
  logoutButton: {
    marginTop: spacing.medium,
  },
})

export default ProfileScreen
