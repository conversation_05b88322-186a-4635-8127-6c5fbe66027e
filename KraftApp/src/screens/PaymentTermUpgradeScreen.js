"use client"

import { useState, useEffect } from "react"
import { View, Text, StyleSheet, ScrollView, TextInput, Alert } from "react-native"
import { colors } from "../theme/colors"
import { spacing } from "../theme/spacing"
import { textStyles } from "../theme/typography"
import StatusBarManager from "../components/common/StatusBarManager"
import Header from "../components/common/Header"
import Button from "../components/common/Button"
import Dropdown from "../components/common/Dropdown"
import { useAuth } from "../store/auth/AuthContext"
import { paymentTermApi } from "../api"
import errorHandler from "../utils/errorHandler"
import alertManager from "../utils/alertManager"

/**
 * Payment Term Upgrade Screen
 * Allows users to request an upgrade to their payment terms
 */
const PaymentTermUpgradeScreen = ({ navigation }) => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    requestedPaymentTerm: "",
    message: "",
  })
  const [errors, setErrors] = useState({})

  // Payment terms options - using backend format
  const allPaymentTermsOptions = [
    { label: "Immediate", value: "IMMEDIATE" },
    { label: "30 Days", value: "THIRTY_DAYS" },
    { label: "60 Days", value: "SIXTY_DAYS" },
  ]

  // Filter out current payment term to prevent selecting the same term
  const paymentTermsOptions = allPaymentTermsOptions.filter(option => {
    const currentTerm = user?.user?.paymentTerms || user?.paymentTerms || "IMMEDIATE";
    return option.value !== currentTerm;
  })

  // Get current payment term for display
  const currentPaymentTerm = user?.user?.paymentTerms || user?.paymentTerms || "Not set"
  const currentPaymentTermLabel = allPaymentTermsOptions.find(
    option => option.value === currentPaymentTerm
  )?.label || currentPaymentTerm.replace("_", " ")

  // Handle form input changes
  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  // Validate form data
  const validateForm = () => {
    const newErrors = {}
    let isValid = true

    // Check if payment term is selected
    if (!formData.requestedPaymentTerm) {
      newErrors.requestedPaymentTerm = "Please select a payment term"
      isValid = false
    }

    // Note: No need to check for same term since we filter it out from options

    // Optional message validation (if provided, should be meaningful)
    if (formData.message && formData.message.trim().length < 10) {
      newErrors.message = "Message should be at least 10 characters if provided"
      isValid = false
    }

    setErrors(newErrors)
    return isValid
  }

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      // Submit payment term upgrade request
      await paymentTermApi.submitUpgradeRequest({
        requestedPaymentTerms: formData.requestedPaymentTerm, // Backend expects 'requestedPaymentTerms'
        reason: formData.message.trim() || "Payment term upgrade request", // Backend expects 'reason'
      })

      // Navigate to success screen
      navigation.navigate("Success", {
        type: 'payment_term_upgrade',
        title: 'Payment Term Upgrade Submitted',
        message: 'Your payment term upgrade request has been submitted for approval. We will notify you once it has been processed.',
        buttonText: 'Back to Profile',
        buttonDestination: 'ProfileTab'
      })
    } catch (error) {
      console.error("Payment term upgrade error:", error)
      errorHandler.handleApiError(error, {
        context: "payment_term_upgrade",
        fallbackMessage: "Failed to submit payment term upgrade request. Please try again.",
        showAlert: true
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />
      
      <Header 
        title="Payment Term Upgrade" 
        onBackPress={() => navigation.goBack()} 
      />

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={styles.currentTermContainer}>
          <Text style={styles.sectionTitle}>Current Payment Terms</Text>
          <Text style={styles.currentTermText}>{currentPaymentTermLabel}</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Request Payment Term Upgrade</Text>
          <Text style={styles.description}>
            Select the payment terms you would like to upgrade to. Your request will be reviewed and you will be notified of the decision.
          </Text>

          <Dropdown
            label="Requested Payment Terms"
            options={paymentTermsOptions}
            selectedValue={formData.requestedPaymentTerm}
            onValueChange={(value) => handleChange("requestedPaymentTerm", value)}
            placeholder="Select payment terms"
            style={styles.dropdown}
          />
          {errors.requestedPaymentTerm && (
            <Text style={styles.errorText}>{errors.requestedPaymentTerm}</Text>
          )}

          <Text style={styles.label}>Additional Comments (Optional)</Text>
          <TextInput
            style={[styles.messageInput, errors.message && styles.inputError]}
            value={formData.message}
            onChangeText={(value) => handleChange("message", value)}
            placeholder="Add any additional comments or justification for your request..."
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
          {errors.message && (
            <Text style={styles.errorText}>{errors.message}</Text>
          )}

          <Button
            title="Send for Approval"
            onPress={handleSubmit}
            loading={loading}
            style={styles.submitButton}
          />
        </View>

        <View style={styles.infoContainer}>
          <Text style={styles.infoTitle}>Important Information</Text>
          <Text style={styles.infoText}>
            • Payment term upgrades are subject to approval based on your account history and creditworthiness.
          </Text>
          <Text style={styles.infoText}>
            • Processing typically takes 2-3 business days.
          </Text>
          <Text style={styles.infoText}>
            • You will be notified via email once your request has been processed.
          </Text>
        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: spacing.medium,
    paddingBottom: spacing.xlarge,
  },
  currentTermContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    marginBottom: spacing.large,
    elevation: 2,
  },
  sectionTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.medium,
  },
  currentTermText: {
    ...textStyles.body1,
    color: colors.primary,
    fontWeight: "600",
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    marginBottom: spacing.large,
    elevation: 2,
  },
  description: {
    ...textStyles.body2,
    color: colors.textLight,
    marginBottom: spacing.large,
    lineHeight: 20,
  },
  dropdown: {
    marginBottom: spacing.medium,
  },
  label: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.small,
    fontWeight: "500",
  },
  messageInput: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.medium,
    marginBottom: spacing.medium,
    ...textStyles.body1,
    minHeight: 100,
  },
  inputError: {
    borderColor: colors.error,
  },
  errorText: {
    ...textStyles.caption,
    color: colors.error,
    marginBottom: spacing.medium,
  },
  submitButton: {
    marginTop: spacing.medium,
  },
  infoContainer: {
    backgroundColor: colors.lightGray || "#f8f9fa",
    borderRadius: 12,
    padding: spacing.medium,
  },
  infoTitle: {
    ...textStyles.heading4,
    color: colors.textDark,
    marginBottom: spacing.medium,
  },
  infoText: {
    ...textStyles.body2,
    color: colors.textLight,
    marginBottom: spacing.small,
    lineHeight: 18,
  },
})

export default PaymentTermUpgradeScreen
