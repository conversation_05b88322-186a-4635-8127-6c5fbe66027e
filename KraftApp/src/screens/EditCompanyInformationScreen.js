"use client"

import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';
import Header from '../components/common/Header';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import { authApi } from '../api';
import { useAuth } from '../store/auth/AuthContext';
import errorHandler from '../utils/errorHandler';
import alertManager from '../utils/alertManager';

const EditCompanyInformationScreen = ({ navigation }) => {
  const { user: authUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    contactPerson: '',
    contactNumber: '',
    email: '',
  });
  const [errors, setErrors] = useState({
    contactPerson: '',
    contactNumber: '',
    email: '',
  });

  // Initialize form data with current user information
  useEffect(() => {
    if (authUser?.user) {
      setFormData({
        contactPerson: authUser.user.contactPerson || '',
        contactNumber: authUser.user.contactNumber || '',
        email: authUser.user.email || '',
      });
    }
  }, [authUser]);

  // Handle input changes
  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {
      contactPerson: '',
      contactNumber: '',
      email: '',
    };
    
    let isValid = true;
    
    // Validate contact person
    if (!formData.contactPerson.trim()) {
      newErrors.contactPerson = 'Contact person name is required';
      isValid = false;
    } else if (formData.contactPerson.trim().length < 3) {
      newErrors.contactPerson = 'Contact person name must be at least 3 characters long';
      isValid = false;
    }
    
    // Validate contact number
    if (!formData.contactNumber.trim()) {
      newErrors.contactNumber = 'Contact number is required';
      isValid = false;
    } else if (!/^[6-9]\d{9}$/.test(formData.contactNumber.trim())) {
      newErrors.contactNumber = 'Please enter a valid 10-digit Indian mobile number';
      isValid = false;
    }
    
    // Validate email
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
      newErrors.email = 'Please enter a valid email address';
      isValid = false;
    }
    
    setErrors(newErrors);
    return isValid;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      // Prepare data for API call
      const updateData = {
        contactPerson: formData.contactPerson.trim(),
        contactNumber: formData.contactNumber.trim(),
        email: formData.email.trim(),
      };
      
      // Call API to update profile
      await authApi.updateProfile(updateData);
      
      // Show success message
      alertManager.showSuccess(
        'Profile Updated',
        'Your company information has been updated successfully.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      // Handle validation errors from API
      if (error.response?.status === 400 && error.response?.data?.errors) {
        const validationErrors = error.response.data.errors;
        const newErrors = { ...errors };
        
        validationErrors.forEach(err => {
          if (err.field && newErrors.hasOwnProperty(err.field)) {
            newErrors[err.field] = err.message;
          }
        });
        
        setErrors(newErrors);
      } else {
        errorHandler.handleApiError(error, {
          context: 'update_profile',
          fallbackMessage: 'Failed to update company information. Please try again.',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />
      
      <Header 
        title="Edit Company Information" 
        onBackPress={() => navigation.goBack()} 
      />

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={styles.formContainer}>
          <Text style={styles.description}>
            Update your company contact information below. This information will be used for all communications and order processing.
          </Text>

          <Input
            label="Contact Person"
            value={formData.contactPerson}
            onChangeText={(text) => handleChange('contactPerson', text)}
            placeholder="Enter contact person name"
            error={errors.contactPerson}
          />

          <Input
            label="Contact Number"
            value={formData.contactNumber}
            onChangeText={(text) => handleChange('contactNumber', text)}
            placeholder="Enter 10-digit mobile number"
            keyboardType="phone-pad"
            maxLength={10}
            error={errors.contactNumber}
          />

          <Input
            label="Email Address"
            value={formData.email}
            onChangeText={(text) => handleChange('email', text)}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
            error={errors.email}
          />

          <View style={styles.buttonContainer}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={() => navigation.goBack()}
              style={styles.button}
            />
            <Button
              title="Update Information"
              onPress={handleSubmit}
              loading={loading}
              style={styles.button}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: spacing.medium,
    paddingBottom: spacing.xlarge,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    elevation: 2,
  },
  description: {
    ...textStyles.body2,
    color: colors.textLight,
    marginBottom: spacing.large,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.large,
  },
  button: {
    flex: 1,
    marginHorizontal: spacing.tiny,
  },
});

export default EditCompanyInformationScreen;
