import React, { useState, useEffect, useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, RefreshControl, Alert } from 'react-native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';
import Icon from 'react-native-vector-icons/Feather';
import Button from '../components/common/Button';
import errorHandler from '../utils/errorHandler';
import { notificationApi } from '../api';
import { useAuth } from '../store/auth/AuthContext';

const NotificationItem = ({ notification, onPress }) => {
  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get icon based on notification type
  const getIcon = (type) => {
    switch (type) {
      case 'ORDER_STATUS':
        return 'package';
      case 'PAYMENT':
        return 'credit-card';
      case 'ACCOUNT_APPROVAL':
        return 'user-check';
      case 'STOCK_UPDATE':
        return 'box';
      case 'PROMOTION':
        return 'tag';
      default:
        return 'bell';
    }
  };

  return (
    <TouchableOpacity 
      style={[
        styles.notificationItem, 
        !notification.isRead && styles.unreadNotification
      ]} 
      onPress={() => onPress(notification)}
    >
      <View style={styles.iconContainer}>
        <Icon name={getIcon(notification.type)} size={24} color={colors.primary} />
      </View>
      <View style={styles.notificationContent}>
        <Text style={styles.notificationTitle}>{notification.title}</Text>
        <Text style={styles.notificationMessage} numberOfLines={2}>{notification.message}</Text>
        <Text style={styles.notificationTime}>{formatDate(notification.createdAt)}</Text>
      </View>
      {!notification.isRead && <View style={styles.unreadDot} />}
    </TouchableOpacity>
  );
};

const NotificationsScreen = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch notifications
  const fetchNotifications = async () => {
    if (!user) return;

    try {
      const response = await notificationApi.getNotifications(1, 20);
      if (response.status === 'success') {
        setNotifications(response.data.notifications || []);
      }
    } catch (error) {
      errorHandler.handleApiError(error, {
        context: 'fetch_notifications',
        fallbackMessage: 'Failed to load notifications. Please try again.',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    fetchNotifications();
  };

  // Handle notification press
  const handleNotificationPress = async (notification) => {
    try {
      // Mark as read if not already read
      if (!notification.isRead) {
        await notificationApi.markAsRead(notification.id);
        setNotifications(prev =>
          prev.map(item =>
            item.id === notification.id
              ? { ...item, isRead: true }
              : item
          )
        );
      }

      // Handle navigation based on notification type
      // This would typically navigate to the relevant screen
      console.log('Notification pressed:', notification);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      await notificationApi.markAllAsRead();
      setNotifications(prev =>
        prev.map(item => ({ ...item, isRead: true }))
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to mark all notifications as read');
    }
  };

  // Refresh notifications every time the screen is focused to ensure fresh data
  useFocusEffect(
    useCallback(() => {
      console.log('Notifications screen focused, refreshing notifications...');
      fetchNotifications();
      return () => {
        // Cleanup function when screen is unfocused
        console.log('Notifications screen unfocused');
      };
    }, [])
  );

  // Count unread notifications
  const unreadCount = notifications.filter(item => !item.isRead).length;

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Notifications</Text>
      </View>

      <View style={styles.content}>
        {loading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Loading notifications...</Text>
          </View>
        ) : notifications.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Icon name="bell-off" size={64} color={colors.textLight} />
            <Text style={styles.emptyText}>No notifications yet</Text>
            <Text style={styles.emptySubtext}>We'll notify you when there's something new</Text>
          </View>
        ) : (
          <>
            <View style={styles.notificationHeader}>
              {unreadCount > 0 && (
                <Text style={styles.unreadCount}>{unreadCount} unread</Text>
              )}
              {unreadCount > 0 && (
                <TouchableOpacity onPress={markAllAsRead}>
                  <Text style={styles.markAllRead}>Mark all as read</Text>
                </TouchableOpacity>
              )}
            </View>
            <FlatList
              data={notifications}
              renderItem={({ item }) => (
                <NotificationItem 
                  notification={item} 
                  onPress={handleNotificationPress} 
                />
              )}
              keyExtractor={item => item.id}
              contentContainerStyle={styles.listContainer}
              refreshing={refreshing}
              onRefresh={handleRefresh}
            />
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.xlarge,
    paddingHorizontal: spacing.large,
    alignItems: 'center',
  },
  headerTitle: {
    ...textStyles.heading2,
    color: colors.white,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    marginTop: -20,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: spacing.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...textStyles.body1,
    color: colors.textLight,
    marginTop: spacing.medium,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.large,
  },
  emptyText: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginTop: spacing.medium,
    marginBottom: spacing.small,
  },
  emptySubtext: {
    ...textStyles.body1,
    color: colors.textLight,
    textAlign: 'center',
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.medium,
  },
  unreadCount: {
    ...textStyles.body2,
    color: colors.textDark,
    fontWeight: '500',
  },
  markAllRead: {
    ...textStyles.body2,
    color: colors.primary,
    fontWeight: '500',
  },
  listContainer: {
    paddingBottom: spacing.large,
  },
  notificationItem: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    marginBottom: spacing.medium,
    elevation: 2,
  },
  unreadNotification: {
    backgroundColor: colors.primaryLight || '#f0f7ff',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.medium,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '500',
    marginBottom: spacing.tiny,
  },
  notificationMessage: {
    ...textStyles.body2,
    color: colors.textLight,
    marginBottom: spacing.small,
  },
  notificationTime: {
    ...textStyles.caption,
    color: colors.textLight,
  },
  unreadDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
    marginLeft: spacing.small,
    alignSelf: 'center',
  },
});

export default NotificationsScreen;
