"use client"

import { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, Alert, Linking, TouchableOpacity } from 'react-native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';
import Header from '../components/common/Header';
import errorHandler from '../utils/errorHandler';
import Icon from 'react-native-vector-icons/Feather';

/**
 * Enhanced Support Screen with contact form and multiple contact options
 */
const SupportScreen = ({ navigation }) => {
  const [subject, setSubject] = useState('');
  const [category, setCategory] = useState('');
  const [message, setMessage] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  // Support categories
  const categoryOptions = [
    { label: "General Inquiry", value: "general" },
    { label: "Order Issues", value: "order" },
    { label: "Payment Issues", value: "payment" },
    { label: "Account Issues", value: "account" },
    { label: "Technical Support", value: "technical" },
    { label: "Billing Questions", value: "billing" },
    { label: "Other", value: "other" },
  ];

  // Handle form input changes
  const handleChange = (field, value) => {
    switch (field) {
      case 'subject':
        setSubject(value);
        break;
      case 'category':
        setCategory(value);
        break;
      case 'message':
        setMessage(value);
        break;
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    let isValid = true;

    if (!category) {
      newErrors.category = 'Please select a category';
      isValid = false;
    }

    if (!subject.trim()) {
      newErrors.subject = 'Please enter a subject';
      isValid = false;
    } else if (subject.trim().length < 5) {
      newErrors.subject = 'Subject must be at least 5 characters';
      isValid = false;
    }

    if (!message.trim()) {
      newErrors.message = 'Please enter your message';
      isValid = false;
    } else if (message.trim().length < 10) {
      newErrors.message = 'Message must be at least 10 characters';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Handle submit support request
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setSubmitting(true);
    try {
      // Simulate API call - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Reset form
      setSubject('');
      setCategory('');
      setMessage('');
      setErrors({});

      // Show success message
      Alert.alert(
        'Request Submitted', 
        'Your support request has been submitted successfully. We\'ll get back to you within 24 hours.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      errorHandler.handleApiError(error, {
        context: 'submit_support',
        fallbackMessage: 'Failed to submit support request. Please try again.',
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle phone call
  const handlePhoneCall = () => {
    const phoneNumber = '+911234567890';
    Linking.openURL(`tel:${phoneNumber}`);
  };

  // Handle email
  const handleEmail = () => {
    const email = '<EMAIL>';
    Linking.openURL(`mailto:${email}`);
  };

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />
      
      <Header 
        title="Support" 
        onBackPress={() => navigation.goBack()} 
      />

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.description}>
          Need help? Choose how you'd like to get in touch with our support team.
        </Text>

        {/* Quick Contact Options */}
        <View style={styles.quickContactContainer}>
          <Text style={styles.sectionTitle}>Quick Contact</Text>
          
          <TouchableOpacity style={styles.contactOption} onPress={handlePhoneCall}>
            <View style={styles.contactIconContainer}>
              <Icon name="phone" size={24} color={colors.primary} />
            </View>
            <View style={styles.contactInfo}>
              <Text style={styles.contactTitle}>Call Us</Text>
              <Text style={styles.contactSubtitle}>+91 1234567890</Text>
              <Text style={styles.contactHours}>Mon-Fri, 9:00 AM - 6:00 PM IST</Text>
            </View>
            <Icon name="chevron-right" size={20} color={colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.contactOption} onPress={handleEmail}>
            <View style={styles.contactIconContainer}>
              <Icon name="mail" size={24} color={colors.primary} />
            </View>
            <View style={styles.contactInfo}>
              <Text style={styles.contactTitle}>Email Us</Text>
              <Text style={styles.contactSubtitle}><EMAIL></Text>
              <Text style={styles.contactHours}>We'll respond within 24 hours</Text>
            </View>
            <Icon name="chevron-right" size={20} color={colors.textLight} />
          </TouchableOpacity>
        </View>

       
        {/* FAQ Link */}
        <TouchableOpacity 
          style={styles.faqContainer}
          onPress={() => navigation.navigate('FAQ')}
        >
          <View style={styles.faqContent}>
            <Icon name="help-circle" size={24} color={colors.primary} />
            <View style={styles.faqText}>
              <Text style={styles.faqTitle}>Check our FAQ</Text>
              <Text style={styles.faqSubtitle}>Find answers to common questions</Text>
            </View>
            <Icon name="chevron-right" size={20} color={colors.textLight} />
          </View>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: spacing.medium,
    paddingBottom: spacing.xlarge,
  },
  description: {
    ...textStyles.body1,
    color: colors.textLight,
    textAlign: 'center',
    marginBottom: spacing.large,
    lineHeight: 22,
  },
  quickContactContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    marginBottom: spacing.large,
    elevation: 2,
  },
  sectionTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.medium,
  },
  contactOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  contactIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.lightGray || '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.medium,
  },
  contactInfo: {
    flex: 1,
  },
  contactTitle: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '600',
    marginBottom: spacing.tiny,
  },
  contactSubtitle: {
    ...textStyles.body2,
    color: colors.primary,
    marginBottom: spacing.tiny,
  },
  contactHours: {
    ...textStyles.caption,
    color: colors.textLight,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    marginBottom: spacing.large,
    elevation: 2,
  },
  dropdown: {
    marginBottom: spacing.medium,
  },
  label: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.small,
    fontWeight: '500',
  },
  input: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.medium,
    marginBottom: spacing.medium,
    ...textStyles.body1,
  },
  messageInput: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.medium,
    marginBottom: spacing.medium,
    ...textStyles.body1,
    minHeight: 120,
  },
  inputError: {
    borderColor: colors.error,
  },
  errorText: {
    ...textStyles.caption,
    color: colors.error,
    marginBottom: spacing.medium,
  },
  submitButton: {
    marginTop: spacing.medium,
  },
  faqContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    elevation: 2,
  },
  faqContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  faqText: {
    flex: 1,
    marginLeft: spacing.medium,
  },
  faqTitle: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '600',
    marginBottom: spacing.tiny,
  },
  faqSubtitle: {
    ...textStyles.body2,
    color: colors.textLight,
  },
});

export default SupportScreen;
