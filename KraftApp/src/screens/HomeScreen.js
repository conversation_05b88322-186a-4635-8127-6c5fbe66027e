'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  Modal,
  ScrollView,
  ActivityIndicator,
  Image,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';
import Button from '../components/common/Button';
import StockItem from '../components/stock/StockItem';
import { stockApi, cartApi, authApi, notificationApi } from '../api';
import { useAuth } from '../store/auth/AuthContext';
import errorHandler from '../utils/errorHandler';
import alertManager from '../utils/alertManager';
import Icon from 'react-native-vector-icons/Feather';
import CustomDrawer from '../components/navigation/CustomDrawer';

const HomeScreen = ({ navigation }) => {
  const [stocks, setStocks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedStock, setSelectedStock] = useState(null);
  const [filters, setFilters] = useState({
    type: '',
    gsm: '',
    bf: '',
  });
  const { user: authUser } = useAuth();
  const [profileData, setProfileData] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [addingToCart, setAddingToCart] = useState(false);
  const [unreadNotificationCount, setUnreadNotificationCount] = useState(0);
  
  // Extract user data properly - either from auth context or from profile data
  const user = profileData?.user || authUser?.user || authUser || null;
  console.log('user in home screen:', user);

  // Extract user details and payment terms
  const userName = user?.user?.contactPerson || user?.contactPerson || 'User';
  const paymentTerms = user?.user?.paymentTerms || user?.paymentTerms || 'IMMEDIATE';

  // Function to get price based on user's payment terms
  const getPriceForPaymentTerms = (stock) => {
    switch (paymentTerms) {
      case 'IMMEDIATE':
        return stock.immediatePrice;
      case '30_DAYS':
        return stock.thirtyDayPrice;
      case '60_DAYS':
        return stock.sixtyDayPrice;
      default:
        return stock.immediatePrice; // Default to immediate price
    }
  };

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) {
      return 'Good Morning';
    }
    if (hour < 18) {
      return 'Good Afternoon';
    }
    return 'Good Evening';
  };

  // Refresh data every time the screen is focused to ensure fresh data
  useFocusEffect(
    useCallback(() => {
      console.log('Home screen focused, refreshing data...');
      fetchProfile();
      fetchStock();
      fetchUnreadNotificationCount();
      return () => {
        // Cleanup function when screen is unfocused
        console.log('Home screen unfocused');
      };
    }, [])
  );

  // Poll for unread notification count every 30 seconds when user is available
  useEffect(() => {
    if (user) {
      // Initial fetch is handled by useFocusEffect above
      // Poll for unread count every 30 seconds
      const interval = setInterval(fetchUnreadNotificationCount, 30000);
      return () => clearInterval(interval);
    }
  }, [user]);

  // Fetch profile from API
  const fetchProfile = async () => {
    setLoading(true);
    try {
      console.log('Fetching profile data...');
      const response = await authApi.getCurrentUser();
      console.log('Profile data received:', response);

      if (response) {
        setProfileData(response);
      } else {
        console.warn('No profile data returned from API');
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      errorHandler.handleApiError(error, {
        context: 'fetch_profile',
        fallbackMessage: 'Failed to load profile data. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch stock from API
  const fetchStock = async () => {
    setLoading(true);
    try {
      const response = await stockApi.getStock();
      console.log('Stock API response:', response);

      // The API returns data in the format: { status: "success", data: { stocks: [...] } }
      if (response?.status === 'success' && Array.isArray(response?.data?.stocks)) {
        setStocks(response.data.stocks);
        console.log("stocks on home page", stocks);
      } else {
        // Handle case where response format is different
        // Try to extract stocks from different response structures
        const stocksData = response?.data?.stocks || response?.stocks || response?.data || [];
        setStocks(Array.isArray(stocksData) ? stocksData : []);
      }
    } catch (error) {
      console.error('Error fetching stock:', error);
      errorHandler.handleApiError(error, {
        context: 'fetch_stock',
        fallbackMessage: 'Failed to load available stock. Please try again.',
      });
      setStocks([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  // Fetch unread notification count
  const fetchUnreadNotificationCount = async () => {
    try {
      const response = await notificationApi.getUnreadCount();
      if (response?.status === 'success') {
        setUnreadNotificationCount(response.data.unreadCount || 0);
      }
    } catch (error) {
      console.error('Error fetching unread notification count:', error);
      // Don't show error to user for this background operation
    }
  };

  // Filter stocks based on search query and filters
  const filteredStocks = Array.isArray(stocks) ? stocks.filter((stock) => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (
        !stock.type.toLowerCase().includes(query) &&
        !stock.gsm.toString().includes(query) &&
        !stock.bf.toString().includes(query)
      ) {
        return false;
      }
    }

    // Type filter
    if (filters.type && stock.type !== filters.type) {
      return false;
    }

    // GSM filter
    if (filters.gsm && stock.gsm !== Number.parseInt(filters.gsm, 10)) {
      return false;
    }

    // BF filter
    if (filters.bf && stock.bf !== Number.parseInt(filters.bf, 10)) {
      return false;
    }

    return true;
  }) : [];

  // Handle stock selection
  const handleSelectStock = (stock) => {
    setSelectedStock(stock);
    setQuantity(1);
  };

  // Handle adding to cart
  const handleAddToCart = async () => {
    if (!selectedStock) {
      return;
    }

    setAddingToCart(true);
    try {
      await cartApi.addToCart({
        stockId: selectedStock.id,
        quantity: quantity,
      });
      setSelectedStock(null);
      // Show success message
      alertManager.showSuccess(
        'Added to Cart',
        `${quantity} roll${quantity > 1 ? 's' : ''} of ${selectedStock.type} ${selectedStock.gsm} GSM added to your cart.`
      );
    } catch (error) {
      errorHandler.handleApiError(error, {
        context: 'add_to_cart',
        fallbackMessage: 'Failed to add item to cart. Please try again.',
      });
      // Additional feedback shown directly to user via alert
      alertManager.showError(
        'Add to Cart Failed',
        'Couldn\'t add the item to your cart. Please try again.'
      );
    } finally {
      setAddingToCart(false);
    }
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      type: '',
      gsm: '',
      bf: '',
    });
    setShowFilters(false);
  };

  // Apply filters
  const applyFilters = () => {
    setShowFilters(false);
    // Filters are already applied through the filteredStocks calculation
  };

  // Render stock item
  const renderStockItem = ({ item }) => <StockItem item={item} onPress={handleSelectStock} paymentTerms={paymentTerms} />;

  // Render filter modal
  const renderFilterModal = () => (
    <Modal visible={showFilters} transparent animationType="slide" onRequestClose={() => setShowFilters(false)}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Filter Stock</Text>
            <TouchableOpacity onPress={() => setShowFilters(false)}>
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView>
            <View style={styles.filterSection}>
              <Text style={styles.filterLabel}>Paper Type</Text>
              <View style={styles.filterOptions}>
                {['NS', 'GY', 'WS'].map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[styles.filterOption, filters.type === type && styles.selectedFilterOption]}
                    onPress={() => setFilters((prev) => ({ ...prev, type: prev.type === type ? '' : type }))}
                  >
                    <Text style={[styles.filterOptionText, filters.type === type && styles.selectedFilterOptionText]}>
                      {type}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.filterSection}>
              <Text style={styles.filterLabel}>GSM</Text>
              <View style={styles.filterOptions}>
                {[80, 100, 120, 140, 180, 200, 230, 250, 280, 300, 320, 330, 350].map((gsm) => (
                  <TouchableOpacity
                    key={gsm}
                    style={[styles.filterOption, filters.gsm === gsm.toString() && styles.selectedFilterOption]}
                    onPress={() =>
                      setFilters((prev) => ({ ...prev, gsm: prev.gsm === gsm.toString() ? '' : gsm.toString() }))
                    }
                  >
                    <Text
                      style={[
                        styles.filterOptionText,
                        filters.gsm === gsm.toString() && styles.selectedFilterOptionText,
                      ]}
                    >
                      {gsm}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.filterSection}>
              <Text style={styles.filterLabel}>Bursting Factor (BF)</Text>
              <View style={styles.filterOptions}>
                {[16, 18, 20, 22, 25, 28, 30, 35].map((bf) => (
                  <TouchableOpacity
                    key={bf}
                    style={[styles.filterOption, filters.bf === bf.toString() && styles.selectedFilterOption]}
                    onPress={() =>
                      setFilters((prev) => ({ ...prev, bf: prev.bf === bf.toString() ? '' : bf.toString() }))
                    }
                  >
                    <Text
                      style={[styles.filterOptionText, filters.bf === bf.toString() && styles.selectedFilterOptionText]}
                    >
                      {bf}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>

          <View style={styles.filterActions}>
            <Button title="Reset" variant="outline" onPress={resetFilters} style={styles.filterButton} />
            <Button title="Apply Filters" onPress={applyFilters} style={styles.filterButton} />
          </View>
        </View>
      </View>
    </Modal>
  );

  // Render add to cart modal
  const renderAddToCartModal = () => {
    if (!selectedStock) {
      return null;
    }

    return (
      <Modal visible={!!selectedStock} transparent animationType="slide" onRequestClose={() => setSelectedStock(null)}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Adding Item to Cart</Text>
              <TouchableOpacity onPress={() => setSelectedStock(null)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.stockDetails}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Type:</Text>
                <Text style={styles.detailValue}>{selectedStock.type}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>GSM:</Text>
                <Text style={styles.detailValue}>{selectedStock.gsm}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>BF:</Text>
                <Text style={styles.detailValue}>{selectedStock.bf}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Width(mm):</Text>
                <Text style={styles.detailValue}>{selectedStock.width}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Price:</Text>
                <Text style={styles.detailValue}>₹{selectedStock.pricePerRoll} per Kg</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Rolls in Stock:</Text>
                <Text style={styles.detailValue}>{selectedStock.rollsAvailable}</Text>
              </View>
            </View>

            <View style={styles.quantityContainer}>
              <Text style={styles.quantityLabel}>Quantity</Text>
              <View style={styles.quantityControls}>
                <TouchableOpacity
                  style={styles.quantityButton}
                  onPress={() => setQuantity((prev) => Math.max(1, prev - 1))}
                  disabled={quantity <= 1}
                >
                  <Text style={styles.quantityButtonText}>-</Text>
                </TouchableOpacity>
                <Text style={styles.quantityText}>{quantity}</Text>
                <TouchableOpacity
                  style={styles.quantityButton}
                  onPress={() => setQuantity((prev) => Math.min(selectedStock.rollsAvailable, prev + 1))}
                  disabled={quantity >= selectedStock.rollsAvailable}
                >
                  <Text style={styles.quantityButtonText}>+</Text>
                </TouchableOpacity>
              </View>
            </View>

            <Button
              title="Add to Cart"
              onPress={handleAddToCart}
              loading={addingToCart}
              style={styles.addToCartButton}
            />
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <CustomDrawer navigation={navigation} user={user}/>
            <View>
              <Text style={styles.greeting}>{getGreeting()}</Text>
              <Text style={styles.userName}>{userName}</Text>
            </View>
          </View>
          <View style={styles.headerRight}>
            <TouchableOpacity
              style={styles.notificationButton}
              onPress={() => navigation.navigate('Notifications')}
            >
              <Icon name="bell" size={24} color={colors.white} />
              {unreadNotificationCount > 0 && (
                <View style={styles.notificationBadge}>
                  <Text style={styles.notificationBadgeText}>
                    {unreadNotificationCount > 99 ? '99+' : unreadNotificationCount}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
            <Image
              source={require('../assets/images/origami.png')}
              style={styles.origamiImage}
              resizeMode="contain"
            />
          </View>
        </View>
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color={colors.textLight} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search"
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={colors.textLight}
          />
        </View>
        <TouchableOpacity style={styles.filterButtonContainer} onPress={() => setShowFilters(true)}>
          <Text style={styles.filterButtonText}>Filters</Text>
          <Icon name="filter" size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <View style={styles.sectionHeaderContainer}>
          <Text style={styles.paymentTermsText}>Payment terms: {paymentTerms}</Text>
          <Text style={styles.sectionTitle}>Available Craft Paper :</Text>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Loading stock...</Text>
          </View>
        ) : filteredStocks.length === 0 ? (
          <View style={styles.emptyContainer}>
            <View style={styles.emptyImageContainer}>
              <Icon name="box" size={40} color={colors.primary} />
            </View>
            <Text style={styles.emptyTitle}>No Stock Items Available</Text>
            <Text style={styles.emptyText}>
              We don't have any kraft paper in stock at the moment. Please check back later or contact us for custom orders.
            </Text>
            <Button
              title="Refresh"
              variant="outline"
              onPress={fetchStock}
              style={styles.refreshButton}
            />
            <TouchableOpacity
              style={styles.contactButton}
              onPress={() => navigation.navigate('Enquiries')}
            >
              <Text style={styles.contactButtonText}>Contact Us</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={filteredStocks}
            renderItem={renderStockItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>

      {renderFilterModal()}
      {renderAddToCartModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.large,
    paddingHorizontal: spacing.large,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationButton: {
    position: 'relative',
    marginRight: spacing.medium,
    padding: spacing.small,
  },
  notificationBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#ff4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.primary,
  },
  notificationBadgeText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  origamiImage: {
    width: 48,
    height: 48,
  },
  greeting: {
    ...textStyles.body1,
    color: colors.white,
    fontWeight: '400',
  },
  userName: {
    ...textStyles.heading3,
    color: '#fcedb6',
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.medium,
    marginVertical: spacing.medium,
  },
  searchInputContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 5,
    paddingHorizontal: spacing.medium,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    marginRight: spacing.medium,
  },
  searchIcon: {
    marginRight: spacing.small,
  },
  searchInput: {
    ...textStyles.body2,
    flex: 1,
    paddingVertical: spacing.small,
    color: colors.text,
  },
  filterButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterButtonText: {
    ...textStyles.body2,
    color: colors.primary,
    fontWeight: '500',
    marginRight: spacing.tiny,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.small,
  },
  sectionHeaderContainer: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    paddingHorizontal: spacing.medium,
    marginBottom: spacing.medium,
  },
  sectionTitle: {
    ...textStyles.heading4,
    color: colors.textDark,
    fontWeight: '600',
  },
  paymentTermsText: {
    ...textStyles.body2,
    color: colors.primary,
  },
  listContainer: {
    paddingBottom: spacing.large,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...textStyles.body1,
    color: colors.textLight,
    marginTop: spacing.medium,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.large,
  },
  emptyImageContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.lightGray || '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.medium,
  },
  emptyTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.small,
    textAlign: 'center',
  },
  emptyText: {
    ...textStyles.body1,
    color: colors.textLight,
    textAlign: 'center',
    marginBottom: spacing.large,
  },
  refreshButton: {
    minWidth: 200,
    marginBottom: spacing.medium,
  },
  contactButton: {
    padding: spacing.small,
    marginTop: spacing.small,
  },
  contactButtonText: {
    ...textStyles.body2,
    color: colors.primary,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: spacing.medium,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.medium,
    paddingBottom: spacing.small,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
  },
  closeButton: {
    fontSize: 20,
    color: colors.textDark,
    padding: spacing.small,
  },
  filterSection: {
    marginBottom: spacing.large,
  },
  filterLabel: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '500',
    marginBottom: spacing.small,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterOption: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 20,
    paddingVertical: spacing.small,
    paddingHorizontal: spacing.medium,
    marginRight: spacing.small,
    marginBottom: spacing.small,
  },
  selectedFilterOption: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterOptionText: {
    ...textStyles.body2,
    color: colors.textDark,
  },
  selectedFilterOptionText: {
    color: colors.white,
  },
  filterActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.medium,
  },
  filterButton: {
    flex: 1,
    marginHorizontal: spacing.tiny,
  },
  stockDetails: {
    marginBottom: spacing.large,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: spacing.small,
  },
  detailLabel: {
    ...textStyles.body2,
    color: colors.textLight,
    width: 120,
  },
  detailValue: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '500',
  },
  quantityContainer: {
    marginBottom: spacing.large,
  },
  quantityLabel: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '500',
    marginBottom: spacing.small,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.card,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityButtonText: {
    fontSize: 20,
    color: colors.textDark,
    fontWeight: 'bold',
  },
  quantityText: {
    ...textStyles.body1,
    color: colors.textDark,
    marginHorizontal: spacing.medium,
    width: 30,
    textAlign: 'center',
  },
  addToCartButton: {
    marginTop: spacing.medium,
  },
});

export default HomeScreen;
