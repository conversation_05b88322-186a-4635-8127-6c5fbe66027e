"use client"

import { useState, useEffect, useCallback } from "react"
import { useFocusEffect } from '@react-navigation/native'
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from "react-native"
import { colors } from "../theme/colors"
import { spacing } from "../theme/spacing"
import { textStyles } from "../theme/typography"
import StatusBarManager from "../components/common/StatusBarManager"
import OrderHistoryItem from "../components/order/OrderHistoryItem"
import OrderDetailsModal from "../components/order/OrderDetailsModal"
import { orderApi } from "../api"
import errorHandler from "../utils/errorHandler"
import { isActiveOrderStatus, isCompletedOrderStatus } from "../utils/orderStatusUtils"

const OrdersScreen = () => {
  const [activeTab, setActiveTab] = useState("current") // 'current' or 'past'
  const [orders, setOrders] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedOrder, setSelectedOrder] = useState(null)

  // Refresh orders every time the screen is focused to ensure fresh data
  useFocusEffect(
    useCallback(() => {
      console.log('Orders screen focused, refreshing orders...');
      fetchOrders();
      return () => {
        // Cleanup function when screen is unfocused
        console.log('Orders screen unfocused');
      };
    }, [activeTab])
  );

  // Fetch orders from API
  const fetchOrders = async () => {
    setLoading(true)
    try {
      // Map UI tabs to valid API order status values
      // Current orders: PENDING, APPROVED, SHIPPED
      // Past orders: DELIVERED, CANCELLED
      const params = {}

      if (activeTab === "current") {
        // For current orders, we want to fetch PENDING, APPROVED, and SHIPPED orders
        // The backend doesn't support multiple status values in one request,
        // so we'll filter the results client-side in the filteredOrders function
      } else {
        // For past orders, same approach
      }

      const response = await orderApi.getOrderHistory(params)
      console.log('Orders API response:', response)

      // Handle different response formats
      if (response?.orders) {
        // Direct orders array format (from the actual API response)
        console.log('Setting orders from response.orders:', response.orders.length, 'orders')
        setOrders(response.orders)
      } else if (response?.status === "success" && response?.data?.orders) {
        // Format with status and data wrapper
        console.log('Setting orders from response.data.orders:', response.data.orders.length, 'orders')
        setOrders(response.data.orders)
      } else if (response?.data) {
        // Another possible format
        console.log('Setting orders from response.data:', Array.isArray(response.data) ? response.data.length : 'unknown', 'orders')
        setOrders(Array.isArray(response.data) ? response.data : [])
      } else if (Array.isArray(response)) {
        // Direct array format
        console.log('Setting orders from direct array:', response.length, 'orders')
        setOrders(response)
      } else {
        // Fallback if response format is unexpected
        console.warn('Unexpected orders response format:', response)
        setOrders([])
      }
    } catch (error) {
      console.error('Error fetching orders:', error)
      errorHandler.handleApiError(error, {
        context: "fetch_orders",
        fallbackMessage: "Failed to load orders. Please try again.",
        showAlert: true
      })
      setOrders([])
    } finally {
      setLoading(false)
    }
  }

  // Handle order details view
  const handleViewOrderDetails = (order) => {
    setSelectedOrder(order)
  }

  // Handle order update (e.g., after cancellation)
  const handleOrderUpdate = () => {
    fetchOrders() // Refresh the orders list
    setSelectedOrder(null) // Close the modal
  }

  // Filter orders based on active tab
  const filteredOrders = orders.filter((order) => {
    if (activeTab === "current") {
      // Current orders - pending, approved, or shipped
      return isActiveOrderStatus(order.status)
    } else {
      // Past orders - delivered or cancelled
      return isCompletedOrderStatus(order.status)
    }
  })

  // Render order item
  const renderOrderItem = ({ item }) => <OrderHistoryItem order={item} onViewDetails={handleViewOrderDetails} />

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Orders</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === "current" && styles.activeTab]}
            onPress={() => setActiveTab("current")}
          >
            <Text style={[styles.tabText, activeTab === "current" && styles.activeTabText]}>Current Orders</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === "past" && styles.activeTab]}
            onPress={() => setActiveTab("past")}
          >
            <Text style={[styles.tabText, activeTab === "past" && styles.activeTabText]}>Past Orders</Text>
          </TouchableOpacity>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Loading orders...</Text>
          </View>
        ) : filteredOrders.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {activeTab === "current" ? "You have no current orders" : "You have no past orders"}
            </Text>
            {activeTab === "current" && (
              <TouchableOpacity style={styles.shopNowButton}>
                <Text style={styles.shopNowText}>Shop Now</Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <FlatList
            data={filteredOrders}
            renderItem={renderOrderItem}
            keyExtractor={(item) => item.id || item.orderNumber}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>

      <OrderDetailsModal
        visible={!!selectedOrder}
        order={selectedOrder}
        onClose={() => setSelectedOrder(null)}
        onOrderUpdate={handleOrderUpdate}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.xlarge,
    paddingHorizontal: spacing.large,
    alignItems: "center",
  },
  headerTitle: {
    ...textStyles.heading2,
    color: colors.white,
    fontWeight: "bold",
  },
  content: {
    flex: 1,
    marginTop: -20,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: spacing.medium,
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: colors.white,
    borderRadius: 8,
    marginBottom: spacing.medium,
    overflow: "hidden",
    elevation: 2,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.medium,
    alignItems: "center",
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  tabText: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "500",
  },
  activeTabText: {
    color: colors.white,
  },
  listContainer: {
    paddingBottom: spacing.large,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    ...textStyles.body1,
    color: colors.textLight,
    marginTop: spacing.medium,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyText: {
    ...textStyles.body1,
    color: colors.textLight,
    marginBottom: spacing.medium,
  },
  shopNowButton: {
    paddingVertical: spacing.small,
    paddingHorizontal: spacing.large,
    backgroundColor: colors.primary,
    borderRadius: 8,
  },
  shopNowText: {
    ...textStyles.body1,
    color: colors.white,
    fontWeight: "500",
  },
})

export default OrdersScreen
