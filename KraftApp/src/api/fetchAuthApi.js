import AsyncStorage from '@react-native-async-storage/async-storage';
import fetchClient from './fetchClient';
import logger from '../utils/simpleLogger';

/**
 * Helper function to store authentication tokens
 * @param {Object} responseData - Response data containing token information
 * @returns {Promise<boolean>} - Whether tokens were stored successfully
 */
const storeAuthTokens = async (responseData) => {
  // Check if we have a token in the response
  if (responseData?.data?.token) {
    await AsyncStorage.setItem('auth_token', responseData.data.token);

    // Store refresh token if available
    if (responseData.data.refreshToken) {
      await AsyncStorage.setItem('refresh_token', responseData.data.refreshToken);
    }
    return true;
  }
  return false;
};

/**
 * Auth API methods using fetch
 */
const fetchAuthApi = {
  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @returns {Promise} - Promise with registration result
   */
  register: async (userData) => {
    return logger.trackApiCall('auth', 'register', async () => {
      const response = await fetchClient.post('/auth/register', userData);
      return response;
    });
  },

  /**
   * Login with email and password
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise} - Promise with login result
   */
  login: async (email, password) => {
    return logger.trackApiCall('auth', 'login', async () => {
      const response = await fetchClient.post('/auth/login/email', { email, password });
      await storeAuthTokens(response);
      return response;
    });
  },

  /**
   * Request OTP for phone verification
   * @param {string} phoneNumber - User phone number
   * @returns {Promise} - Promise with OTP request result
   */
  requestPhoneOtp: async (phoneNumber) => {
    return logger.trackApiCall('auth', 'requestPhoneOtp', async () => {
      const formattedPhone = phoneNumber;
      logger.info('Sending OTP to phone number:', formattedPhone);

      const response = await fetchClient.post('/auth/phone/send-otp', {
        phoneNumber: formattedPhone,
      });
      return response;
    });
  },

  /**
   * Verify phone OTP
   * @param {string} phoneNumber - User phone number
   * @param {string} verificationId - Verification ID from requestPhoneOtp
   * @param {string} code - One-time password
   * @returns {Promise} - Promise with verification result
   */
  verifyPhoneOtp: async (phoneNumber, verificationId, code) => {
    return logger.trackApiCall('auth', 'verifyPhoneOtp', async () => {
      const formattedPhone = phoneNumber;
      logger.info('Verifying OTP for phone number:', formattedPhone);

      const response = await fetchClient.post('/auth/verify-phone-otp', {
        phoneNumber: formattedPhone,
        verificationId,
        code,
      });
      await storeAuthTokens(response);
      return response;
    });
  },

  /**
   * Request OTP for email verification
   * @param {string} email - User email
   * @returns {Promise} - Promise with OTP request result
   */
  requestEmailOtp: async (email) => {
    return logger.trackApiCall('auth', 'requestEmailOtp', async () => {
      const response = await fetchClient.post('/auth/send-otp', { email });
      return response;
    });
  },

  /**
   * Verify email OTP
   * @param {string} email - User email
   * @param {string} code - One-time password
   * @returns {Promise} - Promise with verification result
   */
  verifyEmailOtp: async (email, code) => {
    return logger.trackApiCall('auth', 'verifyEmailOtp', async () => {
      const response = await fetchClient.post('/auth/verify-otp', { email, code });
      return response;
    });
  },

  /**
   * Check if user is logged in
   * @returns {Promise<boolean>} - Promise with login status
   */
  isLoggedIn: async () => {
    return logger.trackApiCall('auth', 'isLoggedIn', async () => {
      const token = await AsyncStorage.getItem('auth_token');
      return !!token;
    });
  },

  /**
   * Get current user data
   * @returns {Promise} - Promise with user data
   */
  getCurrentUser: async () => {
    return logger.trackApiCall('auth', 'getCurrentUser', async () => {
      try {
        const response = await fetchClient.get('/users/profile');
        return response.data;
      } catch (error) {
        logger.error('Failed to get user profile:', error);
        throw error;
      }
    });
  },

  /**
   * Logout the current user
   * @returns {Promise} - Promise with logout result
   */
  logout: async () => {
    return logger.trackApiCall('auth', 'logout', async () => {
      // Just clear local storage
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('refresh_token');

      return {
        status: 'success',
        message: 'Logged out successfully',
      };
    });
  },

  /**
   * Request password reset
   * @param {string} email - User email
   * @returns {Promise} - Promise with password reset request result
   */
  forgotPassword: async (email) => {
    return logger.trackApiCall('auth', 'forgotPassword', async () => {
      const response = await fetchClient.post('/auth/forgot-password', { email });
      return response;
    });
  },

  /**
   * Reset password with token
   * @param {string} token - Reset token from email
   * @param {string} password - New password
   * @returns {Promise} - Promise with password reset result
   */
  resetPassword: async (token, password) => {
    return logger.trackApiCall('auth', 'resetPassword', async () => {
      const response = await fetchClient.post('/auth/reset-password', { token, password });
      return response;
    });
  },

  /**
   * Login with phone using Firebase UID
   * @param {string} uid - Firebase UID from successful phone verification
   * @returns {Promise} - Promise with login result
   */
  loginWithPhone: async (uid) => {
    return logger.trackApiCall('auth', 'loginWithPhone', async () => {
      const response = await fetchClient.post('/auth/login/phone', { uid });
      await storeAuthTokens(response);
      return response;
    });
  },

  /**
   * Change user password
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise} - Promise with change password result
   */
  changePassword: async (currentPassword, newPassword) => {
    return logger.trackApiCall('auth', 'changePassword', async () => {
      const response = await fetchClient.post('/users/change-password', {
        currentPassword,
        newPassword
      });
      return response;
    });
  },

  /**
   * Update user profile
   * @param {Object} profileData - Profile data to update
   * @param {string} profileData.contactPerson - Contact person name
   * @param {string} profileData.contactNumber - Contact number
   * @param {string} profileData.email - Email address
   * @returns {Promise} - Promise with update profile result
   */
  updateProfile: async (profileData) => {
    return logger.trackApiCall('auth', 'updateProfile', async () => {
      const response = await fetchClient.put('/users/profile', profileData);
      return response;
    });
  },
};

export default fetchAuthApi;
