
// Production configuration
export const API_BASE_URL = 'http://192.168.0.111:5000/api';
// export const API_BASE_URL = 'https://api.arecacorp.com/api';
export const API_TIMEOUT = 30000; // 30 seconds
export const FIREBASE_WEB_API_KEY = 'AIzaSyD76nDRe_JA64yw3ulXPhxpNnTMcy7qmoQ';
export const RECAPTCHA_ENABLED = true;
export const isProductionEnvironment = true;

// User-friendly error messages
export const ERROR_MESSAGES = {
  network: 'Network error. Please check your internet connection.',
  timeout: 'Request timed out. Please try again.',
  server: 'Server error. Please try again later.',
  auth: 'Authentication error. Please log in again.',
  validation: 'Validation error. Please check your input.',
  recaptchaFailed: 'Security verification failed. Please try again.',
  otpExpired: 'Verification code has expired. Please request a new one.',
  otpInvalid: 'Invalid verification code. Please check and try again.',
  default: 'Something went wrong. Please try again.',
};

// Format phone number with country code if needed
export const formatPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return '';
  
  // If already has a + prefix, assume it has a country code
  if (phoneNumber.startsWith('+')) return phoneNumber;
  
  // Default to India country code (+91)
  return `${phoneNumber}`;
};